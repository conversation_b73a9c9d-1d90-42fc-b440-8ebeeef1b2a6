import tkinter as tk
from tkinter import ttk, messagebox, Menu, filedialog
import os
import csv
import datetime
import qrcode
from PIL import Image, ImageTk
from io import BytesIO
import base64
from enhanced_db import Database

# Initialize database
db = Database('hq_logistics.db')

class CommandFacilitiesApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title('Command Facilities Manager')
        self.geometry("1200x800")
        self.minsize(1000, 600)
        self.configure(bg="#f0f0f0")

        # Center the window on screen
        self.center_window()

        # Ensure window is visible and on top
        self.lift()
        self.attributes('-topmost', True)
        self.after_idle(self.attributes, '-topmost', False)

        # Set application icon
        # self.iconbitmap('icon.ico')  # Uncomment and add icon file if available

        # Initialize database connection
        self.db = db
        self.conn = self.db.conn
        self.cur = self.db.cur

        # Create main menu
        self.create_menu()

        # Create status bar
        self.status_text = tk.StringVar()
        self.status_text.set('Welcome to Command Facilities Manager')
        self.status_bar = tk.Label(self, textvariable=self.status_text, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs for CSO-focused modules
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.financial_frame = ttk.Frame(self.notebook)
        self.guest_house_frame = ttk.Frame(self.notebook)
        self.business_frame = ttk.Frame(self.notebook)
        self.members_frame = ttk.Frame(self.notebook)
        self.reports_frame = ttk.Frame(self.notebook)

        # Add tabs to notebook - CSO focused workflow
        self.notebook.add(self.dashboard_frame, text="Executive Dashboard")
        self.notebook.add(self.financial_frame, text="Mess Finances")
        self.notebook.add(self.guest_house_frame, text="Guest House")
        self.notebook.add(self.business_frame, text="Business Operations")
        self.notebook.add(self.members_frame, text="Members & Communication")
        self.notebook.add(self.reports_frame, text="Reports & Analytics")

        # Initialize all tabs with CSO-focused content
        self.setup_executive_dashboard()
        self.setup_mess_finances()
        self.setup_guest_house_cso_view()
        self.setup_business_operations()
        self.setup_members_communication()
        self.setup_reports_analytics()

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_menu(self):
        """Create the main menu bar"""
        self.menu_bar = Menu(self)
        self.config(menu=self.menu_bar)

        # File menu
        file_menu = Menu(self.menu_bar, tearoff=0)
        file_menu.add_command(label="Export Data", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.destroy)
        self.menu_bar.add_cascade(label="File", menu=file_menu)

        # Edit menu
        edit_menu = Menu(self.menu_bar, tearoff=0)
        edit_menu.add_command(label="Preferences", command=self.show_preferences)
        self.menu_bar.add_cascade(label="Edit", menu=edit_menu)

        # Reports menu
        reports_menu = Menu(self.menu_bar, tearoff=0)
        reports_menu.add_command(label="Inventory Report", command=lambda: self.generate_report("inventory"))
        reports_menu.add_command(label="Sales Report", command=lambda: self.generate_report("sales"))
        reports_menu.add_command(label="Gym Usage Report", command=lambda: self.generate_report("gym"))
        reports_menu.add_command(label="Guest House Occupancy", command=lambda: self.generate_report("guest_house"))
        self.menu_bar.add_cascade(label="Reports", menu=reports_menu)

        # Help menu
        help_menu = Menu(self.menu_bar, tearoff=0)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
        help_menu.add_command(label="About", command=self.show_about)
        self.menu_bar.add_cascade(label="Help", menu=help_menu)

    def on_tab_changed(self, event):
        """Handle tab change events"""
        tab_id = self.notebook.select()
        tab_name = self.notebook.tab(tab_id, "text")
        self.status_text.set(f"Current module: {tab_name}")

        # Refresh data in the selected tab
        if tab_name == "Executive Dashboard":
            self.refresh_executive_dashboard()
        elif tab_name == "Mess Finances":
            self.refresh_mess_finances()
        elif tab_name == "Guest House":
            self.refresh_guest_house_cso_view()
        elif tab_name == "Business Operations":
            self.refresh_business_operations()
        elif tab_name == "Members & Communication":
            self.refresh_members_communication()
        elif tab_name == "Reports & Analytics":
            self.refresh_reports_analytics()

    def export_data(self):
        """Export data to CSV based on current tab"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")

        filename = filedialog.asksaveasfilename(
            initialdir="/",
            title=f"Export {current_tab} Data",
            filetypes=(("CSV files", "*.csv"), ("All files", "*.*"))
        )

        if not filename:
            return

        if not filename.endswith('.csv'):
            filename += '.csv'

        try:
            with open(filename, 'w', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)

                if current_tab == "Inventory":
                    # Export inventory data
                    csv_writer.writerow(['ID', 'Item Name', 'Category', 'Supplier', 'Price',
                                        'Quantity', 'Expiration Date', 'Purchase Date'])
                    for item in db.fetch_inventory():
                        # Skip QR code and notes in export
                        csv_writer.writerow([item[0], item[1], item[2], item[3], item[4],
                                            item[5], item[6], item[7]])

                # Add export logic for other tabs as needed

            self.status_text.set(f'{current_tab} data exported to {os.path.basename(filename)}')
            messagebox.showinfo("Export Successful", f"Data exported to {filename}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting data: {str(e)}")

    def show_preferences(self):
        """Show preferences dialog"""
        preferences_window = tk.Toplevel(self)
        preferences_window.title("Preferences")
        preferences_window.geometry("400x300")
        preferences_window.transient(self)
        preferences_window.grab_set()

        # Add preferences options here
        ttk.Label(preferences_window, text="Application Preferences", font=("Arial", 14, "bold")).pack(pady=10)

        # Example preference options
        ttk.Checkbutton(preferences_window, text="Enable notifications").pack(anchor=tk.W, padx=20, pady=5)
        ttk.Checkbutton(preferences_window, text="Dark mode").pack(anchor=tk.W, padx=20, pady=5)

        ttk.Button(preferences_window, text="Save", command=preferences_window.destroy).pack(pady=20)

    def generate_report(self, report_type):
        """Generate and display reports"""
        report_window = tk.Toplevel(self)
        report_window.title(f"{report_type.title()} Report")
        report_window.geometry("800x600")
        report_window.transient(self)

        # Add report generation logic based on type
        ttk.Label(report_window, text=f"{report_type.title()} Report", font=("Arial", 16, "bold")).pack(pady=10)

        # Example report content
        report_frame = ttk.Frame(report_window)
        report_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Add report-specific content here

        # Export button
        ttk.Button(report_window, text="Export Report",
                  command=lambda: self.export_report(report_type)).pack(pady=10)

    def export_report(self, report_type):
        """Export a report to CSV or PDF"""
        # Implementation would depend on report type
        messagebox.showinfo("Export Report", f"{report_type.title()} report export functionality to be implemented")

    def show_user_guide(self):
        """Show user guide"""
        guide_window = tk.Toplevel(self)
        guide_window.title("User Guide")
        guide_window.geometry("800x600")
        guide_window.transient(self)

        # Add user guide content
        ttk.Label(guide_window, text="Command Facilities Manager - User Guide",
                 font=("Arial", 16, "bold")).pack(pady=10)

        # Create a scrollable text widget for the guide
        guide_frame = ttk.Frame(guide_window)
        guide_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        scrollbar = ttk.Scrollbar(guide_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        guide_text = tk.Text(guide_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set)
        guide_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=guide_text.yview)

        # Add guide content
        guide_content = """
        # Command Facilities Manager User Guide

        ## Overview
        This application manages various facilities at the command including inventory,
        restaurants, gym, and guest house.

        ## Modules

        ### Inventory
        - Add, update, and remove items from inventory
        - Track quantities and expiration dates
        - Generate QR codes for items

        ### Restaurants
        - Manage menu items for Hub Mess and Ola Saad restaurants
        - Process food orders
        - Generate invoices for purchases

        ### Gym
        - Track memberships
        - Record check-in and check-out times
        - Monitor usage statistics

        ### Guest House
        - Manage room bookings
        - Track occupancy
        - Process payments

        ### Customers
        - Maintain customer database
        - Track purchase history

        ### Invoices
        - Generate invoices for all transactions
        - Track payment status
        """

        guide_text.insert(tk.END, guide_content)
        guide_text.config(state=tk.DISABLED)  # Make read-only

    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo(
            "About Command Facilities Manager",
            "Command Facilities Manager\nVersion 1.0\n\n"
            "A comprehensive management system for military base facilities including "
            "inventory, restaurants, gym, and guest house."
        )

    # ===== EXECUTIVE DASHBOARD TAB =====
    def setup_executive_dashboard(self):
        """Setup the CSO Executive Dashboard"""
        # Main container with scrollable content
        main_container = ttk.Frame(self.dashboard_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Welcome header
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(header_frame, text="Executive Dashboard",
                 font=("Arial", 24, "bold")).pack(anchor=tk.W)
        ttk.Label(header_frame, text="Chief Staff Officer / President Mess Committee",
                 font=("Arial", 12, "italic")).pack(anchor=tk.W)

        # Quick stats cards
        stats_frame = ttk.Frame(main_container)
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        # Configure grid weights
        for i in range(4):
            stats_frame.columnconfigure(i, weight=1)

        # Real-time Financial Overview Cards
        self.create_stat_card(stats_frame, 0, 0, "Mess Account Balance", "₦2,450,000", "success")
        self.create_stat_card(stats_frame, 0, 1, "Today's Bar Sales", "₦125,000", "info")
        self.create_stat_card(stats_frame, 0, 2, "Guest House Occupancy", "4/6 Rooms", "warning")
        self.create_stat_card(stats_frame, 0, 3, "Wine Account Balance", "₦380,000", "success")

        # Quick Actions Section
        actions_frame = ttk.LabelFrame(main_container, text="Quick Actions", padding=15)
        actions_frame.pack(fill=tk.X, pady=(0, 20))

        actions_grid = ttk.Frame(actions_frame)
        actions_grid.pack(fill=tk.X)

        for i in range(3):
            actions_grid.columnconfigure(i, weight=1)

        # Action buttons
        ttk.Button(actions_grid, text="💬 Send Message to Wine Officer",
                  command=self.message_wine_officer).grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="📊 View Financial Report",
                  command=self.view_financial_report).grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="🏠 Check Guest House Status",
                  command=self.check_guest_house).grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        ttk.Button(actions_grid, text="📢 Send General Announcement",
                  command=self.send_announcement).grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="💰 Review Business Rent Payments",
                  command=self.review_rent_payments).grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="👥 Manage Honorary Members",
                  command=self.manage_honorary_members).grid(row=1, column=2, padx=5, pady=5, sticky="ew")

        # New revolutionary features row
        ttk.Button(actions_grid, text="🌐 Online Booking Portal",
                  command=self.open_online_booking_portal).grid(row=2, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="🍺 Bar Ordering System",
                  command=self.open_bar_ordering_system).grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        ttk.Button(actions_grid, text="🎉 Social Secretary Portal",
                  command=self.open_social_secretary_portal).grid(row=2, column=2, padx=5, pady=5, sticky="ew")

        # Recent Activities
        activities_frame = ttk.LabelFrame(main_container, text="Recent Activities", padding=15)
        activities_frame.pack(fill=tk.BOTH, expand=True)

        # Activities list
        activities_list = ttk.Treeview(activities_frame, columns=("Time", "Activity", "Status"), show="headings", height=8)
        activities_list.heading("Time", text="Time")
        activities_list.heading("Activity", text="Activity")
        activities_list.heading("Status", text="Status")

        activities_list.column("Time", width=120)
        activities_list.column("Activity", width=400)
        activities_list.column("Status", width=100)

        # Real-time activities data
        sample_activities = [
            ("11:45 AM", "Bar Sales: ₦45,000 (Beer: 15 bottles, Wine: 8 bottles)", "Completed"),
            ("11:30 AM", "Guest House Room 102 checked in - Maj. Williams (HM)", "Completed"),
            ("10:45 AM", "Bar Inventory Alert: Heineken below minimum (5 bottles left)", "Alert"),
            ("10:30 AM", "Wine Officer daily sales report submitted", "Completed"),
            ("09:45 AM", "Welfare Shop monthly rent payment received - ₦150,000", "Completed"),
            ("09:15 AM", "Bar Purchase Order: ₦200,000 for new stock", "Pending CSO Approval"),
            ("08:30 AM", "Restaurant monthly rent payment overdue - ₦100,000", "Alert"),
            ("Yesterday", "Honorary member application from Cdr. Smith", "Under Review")
        ]

        # Store activities list for real-time updates
        self.activities_list = activities_list
        self.real_time_activities = sample_activities.copy()

        for activity in sample_activities:
            activities_list.insert("", tk.END, values=activity)

        activities_list.pack(fill=tk.BOTH, expand=True)

        # Start real-time updates
        self.start_real_time_updates()

    def create_stat_card(self, parent, row, col, title, value, style):
        """Create a statistics card"""
        card_frame = ttk.LabelFrame(parent, text=title, padding=10)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

        # Value label with large font
        value_label = ttk.Label(card_frame, text=value, font=("Arial", 18, "bold"))
        value_label.pack()

        # Style indicator (you could add colors here with custom styles)
        if style == "success":
            indicator = "✅"
        elif style == "warning":
            indicator = "⚠️"
        elif style == "danger":
            indicator = "🔴"
        else:
            indicator = "ℹ️"

        ttk.Label(card_frame, text=indicator, font=("Arial", 12)).pack()

    def refresh_executive_dashboard(self):
        """Refresh dashboard data"""
        # This would update the dashboard with real-time data
        self.status_text.set("Executive Dashboard - Real-time data updated")

    # Quick action methods
    def message_wine_officer(self):
        """Open messaging interface for Wine Officer"""
        self.open_messaging_window("Wine Officer", "<EMAIL>")

    def view_financial_report(self):
        """Open comprehensive financial report"""
        self.open_financial_report_window()

    def check_guest_house(self):
        """Quick check of guest house status"""
        # Switch to guest house tab
        self.notebook.select(2)  # Guest House tab
        self.status_text.set("Switched to Guest House management")

    def send_announcement(self):
        """Send general announcement"""
        self.open_announcement_window()

    def review_rent_payments(self):
        """Review business rent payments"""
        self.open_rent_payments_window()

    def manage_honorary_members(self):
        """Manage honorary members"""
        self.open_honorary_members_window()

    def open_messaging_window(self, recipient, email):
        """Open messaging window for communication"""
        msg_window = tk.Toplevel(self)
        msg_window.title(f"Message {recipient}")
        msg_window.geometry("500x400")
        msg_window.transient(self)
        msg_window.grab_set()

        # Header
        header_frame = ttk.Frame(msg_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text=f"Send Message to {recipient}",
                 font=("Arial", 16, "bold")).pack()
        ttk.Label(header_frame, text=f"Email: {email}",
                 font=("Arial", 10)).pack()

        # Message form
        form_frame = ttk.LabelFrame(msg_window, text="Message Details", padding=15)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Subject
        ttk.Label(form_frame, text="Subject:").pack(anchor=tk.W)
        subject_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=subject_var, width=50).pack(fill=tk.X, pady=5)

        # Message body
        ttk.Label(form_frame, text="Message:").pack(anchor=tk.W, pady=(10, 0))
        message_text = tk.Text(form_frame, height=10, width=50)
        message_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Priority
        priority_frame = ttk.Frame(form_frame)
        priority_frame.pack(fill=tk.X, pady=10)

        ttk.Label(priority_frame, text="Priority:").pack(side=tk.LEFT)
        priority_var = tk.StringVar(value="Normal")
        priority_combo = ttk.Combobox(priority_frame, textvariable=priority_var, width=15)
        priority_combo['values'] = ('Low', 'Normal', 'High', 'Urgent')
        priority_combo.pack(side=tk.LEFT, padx=10)

        # Buttons
        button_frame = ttk.Frame(msg_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Send Message",
                  command=lambda: self.send_message(recipient, subject_var.get(),
                                                  message_text.get("1.0", tk.END),
                                                  priority_var.get(), msg_window)).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel",
                  command=msg_window.destroy).pack(side=tk.RIGHT, padx=5)

    def send_message(self, recipient, subject, message, priority, window):
        """Send message to recipient"""
        if not subject.strip() or not message.strip():
            messagebox.showerror("Error", "Please fill in subject and message")
            return

        # Here you would integrate with actual messaging system
        messagebox.showinfo("Message Sent",
                           f"Message sent successfully to {recipient}!\n\n"
                           f"Subject: {subject}\n"
                           f"Priority: {priority}\n"
                           f"Message will be delivered via email and internal system.")

        window.destroy()
        self.status_text.set(f"Message sent to {recipient}")

    def open_financial_report_window(self):
        """Open comprehensive financial report window"""
        report_window = tk.Toplevel(self)
        report_window.title("Financial Report - Command Facilities Manager")
        report_window.geometry("800x600")
        report_window.transient(self)

        # Header
        header_frame = ttk.Frame(report_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text="Comprehensive Financial Report",
                 font=("Arial", 18, "bold")).pack()
        ttk.Label(header_frame, text=f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}",
                 font=("Arial", 10)).pack()

        # Create notebook for different reports
        notebook = ttk.Notebook(report_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Monthly P&L Tab
        pl_frame = ttk.Frame(notebook)
        notebook.add(pl_frame, text="Monthly P&L")

        pl_text = tk.Text(pl_frame, wrap=tk.WORD)
        pl_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        pl_report = """COMMAND FACILITIES MANAGER - MONTHLY P&L STATEMENT

REVENUE STREAMS:
• Guest House Revenue:           ₦450,000
• Hub Mess Bar Sales:           ₦850,000
• Welfare Shop Rent:            ₦150,000
• Restaurant Rent:              ₦100,000
• Mess Membership Fees:         ₦200,000
• Event Hosting:                ₦75,000
TOTAL REVENUE:                  ₦1,825,000

EXPENSES:
• Staff Salaries:               ₦400,000
• Utilities:                    ₦150,000
• Food & Beverage Costs:        ₦300,000
• Maintenance:                  ₦100,000
• Administrative:               ₦75,000
• Equipment:                    ₦50,000
TOTAL EXPENSES:                 ₦1,075,000

NET PROFIT:                     ₦750,000
Profit Margin:                  41.1%

ACCOUNT BALANCES:
• Main Mess Account:            ₦2,450,000
• Wine Account:                 ₦380,000
• Reserve Fund:                 ₦500,000
TOTAL ASSETS:                   ₦3,330,000"""

        pl_text.insert("1.0", pl_report)
        pl_text.config(state=tk.DISABLED)

        # Cash Flow Tab
        cf_frame = ttk.Frame(notebook)
        notebook.add(cf_frame, text="Cash Flow")

        cf_text = tk.Text(cf_frame, wrap=tk.WORD)
        cf_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        cf_report = """CASH FLOW ANALYSIS - CURRENT MONTH

CASH INFLOWS:
Daily Bar Sales Average:        ₦28,333
Guest House Bookings:           ₦15,000/day
Monthly Rent Collections:       ₦250,000
Membership Fees:                ₦200,000

CASH OUTFLOWS:
Daily Operating Expenses:       ₦12,000
Staff Payments:                 ₦400,000/month
Supplier Payments:              ₦300,000/month
Utilities & Maintenance:        ₦250,000/month

NET CASH FLOW:                  +₦750,000/month

PROJECTED 3-MONTH OUTLOOK:
Month 1:                        +₦750,000
Month 2:                        +₦780,000
Month 3:                        +₦810,000

RECOMMENDATIONS:
• Maintain current revenue streams
• Consider expanding guest house capacity
• Optimize bar inventory management
• Explore additional revenue opportunities"""

        cf_text.insert("1.0", cf_report)
        cf_text.config(state=tk.DISABLED)

        # Action buttons
        button_frame = ttk.Frame(report_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Export to PDF",
                  command=lambda: messagebox.showinfo("Export", "Report exported to PDF successfully!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Email Report",
                  command=lambda: messagebox.showinfo("Email", "Report emailed to Financial Member!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Print",
                  command=lambda: messagebox.showinfo("Print", "Report sent to printer!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close",
                  command=report_window.destroy).pack(side=tk.RIGHT, padx=5)

    def open_announcement_window(self):
        """Open general announcement window"""
        ann_window = tk.Toplevel(self)
        ann_window.title("Send General Announcement")
        ann_window.geometry("600x500")
        ann_window.transient(self)
        ann_window.grab_set()

        # Header
        header_frame = ttk.Frame(ann_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text="Send General Announcement",
                 font=("Arial", 16, "bold")).pack()
        ttk.Label(header_frame, text="Broadcast message to all members and staff",
                 font=("Arial", 10)).pack()

        # Recipients selection
        recipients_frame = ttk.LabelFrame(ann_window, text="Recipients", padding=15)
        recipients_frame.pack(fill=tk.X, padx=20, pady=10)

        # Checkboxes for different groups
        all_staff_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(recipients_frame, text="All Staff Members", variable=all_staff_var).pack(anchor=tk.W)

        honorary_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(recipients_frame, text="Honorary Mess Members", variable=honorary_var).pack(anchor=tk.W)

        officers_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(recipients_frame, text="Military Officers", variable=officers_var).pack(anchor=tk.W)

        guests_var = tk.BooleanVar()
        ttk.Checkbutton(recipients_frame, text="Current Guests", variable=guests_var).pack(anchor=tk.W)

        # Announcement form
        form_frame = ttk.LabelFrame(ann_window, text="Announcement Details", padding=15)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Title
        ttk.Label(form_frame, text="Title:").pack(anchor=tk.W)
        title_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=title_var, width=60).pack(fill=tk.X, pady=5)

        # Message
        ttk.Label(form_frame, text="Message:").pack(anchor=tk.W, pady=(10, 0))
        message_text = tk.Text(form_frame, height=8, width=60)
        message_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Priority and type
        options_frame = ttk.Frame(form_frame)
        options_frame.pack(fill=tk.X, pady=10)

        ttk.Label(options_frame, text="Type:").pack(side=tk.LEFT)
        type_var = tk.StringVar(value="General")
        type_combo = ttk.Combobox(options_frame, textvariable=type_var, width=15)
        type_combo['values'] = ('General', 'Event', 'Emergency', 'Maintenance', 'Policy')
        type_combo.pack(side=tk.LEFT, padx=10)

        ttk.Label(options_frame, text="Priority:").pack(side=tk.LEFT, padx=(20, 0))
        priority_var = tk.StringVar(value="Normal")
        priority_combo = ttk.Combobox(options_frame, textvariable=priority_var, width=15)
        priority_combo['values'] = ('Low', 'Normal', 'High', 'Urgent')
        priority_combo.pack(side=tk.LEFT, padx=10)

        # Buttons
        button_frame = ttk.Frame(ann_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Send Announcement",
                  command=lambda: self.send_announcement_message(
                      title_var.get(), message_text.get("1.0", tk.END),
                      type_var.get(), priority_var.get(),
                      [all_staff_var.get(), honorary_var.get(), officers_var.get(), guests_var.get()],
                      ann_window)).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel",
                  command=ann_window.destroy).pack(side=tk.RIGHT, padx=5)

    def send_announcement_message(self, title, message, ann_type, priority, recipients, window):
        """Send announcement to selected recipients"""
        if not title.strip() or not message.strip():
            messagebox.showerror("Error", "Please fill in title and message")
            return

        recipient_groups = []
        if recipients[0]: recipient_groups.append("All Staff")
        if recipients[1]: recipient_groups.append("Honorary Members")
        if recipients[2]: recipient_groups.append("Officers")
        if recipients[3]: recipient_groups.append("Guests")

        if not any(recipients):
            messagebox.showerror("Error", "Please select at least one recipient group")
            return

        messagebox.showinfo("Announcement Sent",
                           f"Announcement sent successfully!\n\n"
                           f"Title: {title}\n"
                           f"Type: {ann_type}\n"
                           f"Priority: {priority}\n"
                           f"Recipients: {', '.join(recipient_groups)}\n\n"
                           f"Message delivered via email, SMS, and notice boards.")

        window.destroy()
        self.status_text.set("General announcement sent to all selected groups")

    def open_rent_payments_window(self):
        """Open rent payments review window"""
        rent_window = tk.Toplevel(self)
        rent_window.title("Business Rent Payments Review")
        rent_window.geometry("700x500")
        rent_window.transient(self)

        # Header
        header_frame = ttk.Frame(rent_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text="Business Rent Payments Review",
                 font=("Arial", 18, "bold")).pack()
        ttk.Label(header_frame, text="Monitor and manage monthly rent payments from businesses",
                 font=("Arial", 10)).pack()

        # Rent payments table
        table_frame = ttk.LabelFrame(rent_window, text="Current Rent Status", padding=15)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        rent_tree = ttk.Treeview(table_frame, columns=("Business", "Monthly Rent", "Last Payment", "Status", "Days Overdue"), show="headings", height=10)
        rent_tree.heading("Business", text="Business")
        rent_tree.heading("Monthly Rent", text="Monthly Rent")
        rent_tree.heading("Last Payment", text="Last Payment")
        rent_tree.heading("Status", text="Status")
        rent_tree.heading("Days Overdue", text="Days Overdue")

        rent_tree.column("Business", width=150)
        rent_tree.column("Monthly Rent", width=120)
        rent_tree.column("Last Payment", width=120)
        rent_tree.column("Status", width=100)
        rent_tree.column("Days Overdue", width=100)

        # Sample rent data
        rent_data = [
            ("Welfare Shop", "₦150,000", "2024-01-15", "✅ Paid", "0"),
            ("Restaurant", "₦100,000", "2023-12-25", "🔴 Overdue", "5"),
            ("Barber Shop", "₦50,000", "2024-01-10", "✅ Paid", "0"),
            ("Laundry Service", "₦75,000", "2024-01-01", "⚠️ Due Soon", "0")
        ]

        for rent in rent_data:
            rent_tree.insert("", tk.END, values=rent)

        rent_tree.pack(fill=tk.BOTH, expand=True, pady=10)

        # Action buttons
        button_frame = ttk.Frame(rent_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Send Payment Reminder",
                  command=lambda: messagebox.showinfo("Reminder", "Payment reminder sent to overdue businesses!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Generate Rent Report",
                  command=lambda: messagebox.showinfo("Report", "Rent collection report generated!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Record Payment",
                  command=lambda: messagebox.showinfo("Payment", "Opening payment recording form...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close",
                  command=rent_window.destroy).pack(side=tk.RIGHT, padx=5)

    def open_honorary_members_window(self):
        """Open honorary members management window"""
        hm_window = tk.Toplevel(self)
        hm_window.title("Honorary Members Management")
        hm_window.geometry("800x600")
        hm_window.transient(self)

        # Header
        header_frame = ttk.Frame(hm_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text="Honorary Members Management",
                 font=("Arial", 18, "bold")).pack()
        ttk.Label(header_frame, text="Manage honorary mess members and applications",
                 font=("Arial", 10)).pack()

        # Create notebook for different sections
        notebook = ttk.Notebook(hm_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Current Members Tab
        members_frame = ttk.Frame(notebook)
        notebook.add(members_frame, text="Current Members")

        # Search frame
        search_frame = ttk.Frame(members_frame)
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=search_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Search",
                  command=lambda: messagebox.showinfo("Search", f"Searching for: {search_var.get()}")).pack(side=tk.LEFT, padx=5)

        # Members table
        members_tree = ttk.Treeview(members_frame, columns=("ID", "Name", "Profession", "Join Date", "Status"), show="headings", height=12)
        members_tree.heading("ID", text="Member ID")
        members_tree.heading("Name", text="Full Name")
        members_tree.heading("Profession", text="Profession")
        members_tree.heading("Join Date", text="Join Date")
        members_tree.heading("Status", text="Status")

        members_tree.column("ID", width=80)
        members_tree.column("Name", width=200)
        members_tree.column("Profession", width=150)
        members_tree.column("Join Date", width=100)
        members_tree.column("Status", width=100)

        # Sample members data
        members_data = [
            ("HM001", "Dr. John Smith", "Medical Doctor", "2020-01-15", "Active"),
            ("HM002", "Eng. Mary Johnson", "Civil Engineer", "2019-06-20", "Active"),
            ("HM003", "Prof. David Wilson", "University Professor", "2021-03-10", "Active"),
            ("HM004", "Mr. James Brown", "Business Executive", "2022-08-05", "Active"),
            ("HM005", "Dr. Sarah Davis", "Veterinarian", "2023-02-12", "Active")
        ]

        for member in members_data:
            members_tree.insert("", tk.END, values=member)

        members_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Pending Applications Tab
        applications_frame = ttk.Frame(notebook)
        notebook.add(applications_frame, text="Pending Applications")

        # Applications table
        apps_tree = ttk.Treeview(applications_frame, columns=("Name", "Profession", "Apply Date", "Sponsors", "Status"), show="headings", height=12)
        apps_tree.heading("Name", text="Applicant Name")
        apps_tree.heading("Profession", text="Profession")
        apps_tree.heading("Apply Date", text="Application Date")
        apps_tree.heading("Sponsors", text="Sponsors")
        apps_tree.heading("Status", text="Status")

        apps_tree.column("Name", width=150)
        apps_tree.column("Profession", width=150)
        apps_tree.column("Apply Date", text="100")
        apps_tree.column("Sponsors", width=200)
        apps_tree.column("Status", width=100)

        # Sample applications data
        applications_data = [
            ("Mr. Robert Taylor", "Architect", "2024-01-10", "Dr. Smith, Eng. Johnson", "Under Review"),
            ("Ms. Lisa Anderson", "Lawyer", "2024-01-08", "Prof. Wilson, Mr. Brown", "Pending Interview"),
            ("Dr. Michael Lee", "Dentist", "2024-01-05", "Dr. Davis, Dr. Smith", "Approved")
        ]

        for app in applications_data:
            apps_tree.insert("", tk.END, values=app)

        apps_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Action buttons
        button_frame = ttk.Frame(hm_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="View Member Profile",
                  command=lambda: messagebox.showinfo("Profile", "Opening member profile...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Process Application",
                  command=lambda: messagebox.showinfo("Application", "Opening application review...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Send Member Message",
                  command=lambda: messagebox.showinfo("Message", "Opening member messaging...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Generate Members Report",
                  command=lambda: messagebox.showinfo("Report", "Members report generated!")).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close",
                  command=hm_window.destroy).pack(side=tk.RIGHT, padx=5)

    # ===== MESS FINANCES TAB =====
    def setup_mess_finances(self):
        """Setup the Mess Finances tab"""
        ttk.Label(self.financial_frame, text="Mess Financial Management",
                 font=("Arial", 20, "bold")).pack(pady=20)

        # Financial overview
        overview_frame = ttk.LabelFrame(self.financial_frame, text="Financial Overview", padding=15)
        overview_frame.pack(fill=tk.X, padx=20, pady=10)

        # Account balance and transactions
        ttk.Label(overview_frame, text="Central Mess Account Balance: ₦2,450,000",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=5)

        # Revenue streams
        revenue_frame = ttk.LabelFrame(self.financial_frame, text="Monthly Revenue Streams", padding=15)
        revenue_frame.pack(fill=tk.X, padx=20, pady=10)

        revenue_data = [
            ("Guest House Revenue", "₦450,000"),
            ("Welfare Shop Rent", "₦150,000"),
            ("Restaurant Rent", "₦100,000"),
            ("Mess Fees", "₦150,000")
        ]

        for source, amount in revenue_data:
            row_frame = ttk.Frame(revenue_frame)
            row_frame.pack(fill=tk.X, pady=2)
            ttk.Label(row_frame, text=source).pack(side=tk.LEFT)
            ttk.Label(row_frame, text=amount, font=("Arial", 10, "bold")).pack(side=tk.RIGHT)

    def refresh_mess_finances(self):
        """Refresh financial data"""
        self.status_text.set("Mess Finances - Financial data updated")

    # ===== BUSINESS OPERATIONS TAB =====
    def setup_business_operations(self):
        """Setup Business Operations tab"""
        ttk.Label(self.business_frame, text="Business Operations Management",
                 font=("Arial", 20, "bold")).pack(pady=20)

        # Hub Mess Bar (Wine Officer Module) - Real-time accounting
        bar_frame = ttk.LabelFrame(self.business_frame, text="🍷 Hub Mess Bar (Wine Officer Module)", padding=15)
        bar_frame.pack(fill=tk.X, padx=20, pady=10)

        # Today's bar performance
        bar_stats_frame = ttk.Frame(bar_frame)
        bar_stats_frame.pack(fill=tk.X, pady=10)

        for i in range(4):
            bar_stats_frame.columnconfigure(i, weight=1)

        self.create_stat_card(bar_stats_frame, 0, 0, "Today's Sales", "₦125,000", "success")
        self.create_stat_card(bar_stats_frame, 0, 1, "Transactions", "45 Sales", "info")
        self.create_stat_card(bar_stats_frame, 0, 2, "Wine Account", "₦380,000", "success")
        self.create_stat_card(bar_stats_frame, 0, 3, "Low Stock Items", "3 Brands", "warning")

        # Real-time bar inventory and sales
        bar_details_frame = ttk.Frame(bar_frame)
        bar_details_frame.pack(fill=tk.X, pady=10)

        # Left side - Current inventory status
        inventory_frame = ttk.LabelFrame(bar_details_frame, text="Current Inventory Status", padding=10)
        inventory_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        inventory_tree = ttk.Treeview(inventory_frame, columns=("Brand", "Qty", "Min", "Status"), show="headings", height=6)
        inventory_tree.heading("Brand", text="Brand")
        inventory_tree.heading("Qty", text="Current Qty")
        inventory_tree.heading("Min", text="Min Level")
        inventory_tree.heading("Status", text="Status")

        inventory_tree.column("Brand", width=120)
        inventory_tree.column("Qty", width=80)
        inventory_tree.column("Min", width=80)
        inventory_tree.column("Status", width=100)

        # Sample real-time inventory data
        inventory_data = [
            ("Heineken", "5", "10", "🔴 Restock"),
            ("Star Beer", "25", "15", "✅ Good"),
            ("Guinness", "8", "10", "⚠️ Low"),
            ("Wine (Red)", "12", "8", "✅ Good"),
            ("Whiskey", "6", "5", "✅ Good"),
            ("Vodka", "3", "5", "⚠️ Low")
        ]

        for item in inventory_data:
            inventory_tree.insert("", tk.END, values=item)

        inventory_tree.pack(fill=tk.BOTH, expand=True)

        # Right side - Today's sales breakdown
        sales_frame = ttk.LabelFrame(bar_details_frame, text="Today's Sales Breakdown", padding=10)
        sales_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        sales_tree = ttk.Treeview(sales_frame, columns=("Time", "Customer", "Items", "Amount"), show="headings", height=6)
        sales_tree.heading("Time", text="Time")
        sales_tree.heading("Customer", text="Customer Type")
        sales_tree.heading("Items", text="Items Sold")
        sales_tree.heading("Amount", text="Amount")

        sales_tree.column("Time", width=80)
        sales_tree.column("Customer", width=100)
        sales_tree.column("Items", width=120)
        sales_tree.column("Amount", width=80)

        # Sample real-time sales data
        sales_data = [
            ("11:45", "Guest House", "2 Beer, 1 Wine", "₦8,500"),
            ("11:30", "Bar Hall", "3 Heineken", "₦4,500"),
            ("11:15", "Officer", "1 Whiskey", "₦12,000"),
            ("10:45", "HM Member", "4 Star Beer", "₦6,000"),
            ("10:30", "Guest House", "2 Wine", "₦15,000"),
            ("10:15", "Bar Hall", "5 Beer", "₦7,500")
        ]

        for sale in sales_data:
            sales_tree.insert("", tk.END, values=sale)

        sales_tree.pack(fill=tk.BOTH, expand=True)

        # Bar actions
        bar_actions_frame = ttk.Frame(bar_frame)
        bar_actions_frame.pack(fill=tk.X, pady=10)

        ttk.Button(bar_actions_frame, text="📊 Daily Sales Report",
                  command=self.view_daily_bar_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(bar_actions_frame, text="📦 Request Restock",
                  command=self.request_bar_restock).pack(side=tk.LEFT, padx=5)
        ttk.Button(bar_actions_frame, text="💰 Wine Account Statement",
                  command=self.view_wine_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(bar_actions_frame, text="📋 End of Day Report",
                  command=self.generate_eod_report).pack(side=tk.LEFT, padx=5)

        # Other Business Units
        other_businesses = [
            ("Welfare Shop", "Independent + Rent", "Active", "₦150,000/month rent", "Last payment: 15 days ago"),
            ("Restaurant", "Independent + Rent", "Active", "₦100,000/month rent", "Payment overdue: 5 days")
        ]

        for name, type_op, status, revenue, payment_status in other_businesses:
            business_frame = ttk.LabelFrame(self.business_frame, text=name, padding=10)
            business_frame.pack(fill=tk.X, padx=20, pady=10)

            ttk.Label(business_frame, text=f"Operation Type: {type_op}").pack(anchor=tk.W)
            ttk.Label(business_frame, text=f"Status: {status}").pack(anchor=tk.W)
            ttk.Label(business_frame, text=f"Revenue: {revenue}").pack(anchor=tk.W)
            ttk.Label(business_frame, text=f"Payment Status: {payment_status}").pack(anchor=tk.W)

    def view_daily_bar_report(self):
        """View detailed daily bar sales report"""
        messagebox.showinfo("Daily Bar Report",
                           "Today's Bar Performance:\n\n"
                           "Total Sales: ₦125,000\n"
                           "Total Transactions: 45\n"
                           "Peak Hours: 11:00 AM - 12:00 PM\n"
                           "Top Selling: Star Beer (15 bottles)\n"
                           "Revenue by Customer:\n"
                           "• Guest House: ₦45,000 (36%)\n"
                           "• Bar Hall: ₦35,000 (28%)\n"
                           "• Officers: ₦25,000 (20%)\n"
                           "• HM Members: ₦20,000 (16%)")

    def request_bar_restock(self):
        """Request bar inventory restock"""
        messagebox.showinfo("Restock Request",
                           "Restock Request Submitted:\n\n"
                           "Items Needed:\n"
                           "• Heineken: 20 bottles (Critical)\n"
                           "• Guinness: 15 bottles (Low)\n"
                           "• Vodka: 10 bottles (Low)\n\n"
                           "Estimated Cost: ₦200,000\n"
                           "Status: Pending CSO Approval")

    def view_wine_account(self):
        """View wine account financial statement"""
        messagebox.showinfo("Wine Account Statement",
                           "Wine Account Financial Summary:\n\n"
                           "Current Balance: ₦380,000\n"
                           "This Month's Revenue: ₦850,000\n"
                           "This Month's Expenses: ₦470,000\n"
                           "Net Profit: ₦380,000\n\n"
                           "Recent Transactions:\n"
                           "• Today's Sales: +₦125,000\n"
                           "• Stock Purchase: -₦150,000\n"
                           "• Staff Payments: -₦45,000")

    def generate_eod_report(self):
        """Generate end of day report"""
        messagebox.showinfo("End of Day Report",
                           "End of Day Report Generated:\n\n"
                           "Sales Summary:\n"
                           "• Total Revenue: ₦125,000\n"
                           "• Cash Sales: ₦85,000\n"
                           "• Credit Sales: ₦40,000\n\n"
                           "Inventory Status:\n"
                           "• Items Sold: 45 units\n"
                           "• Low Stock Alerts: 3 items\n"
                           "• Restock Required: Yes\n\n"
                           "Report sent to CSO and Financial Member")

    def refresh_business_operations(self):
        """Refresh business operations data"""
        self.status_text.set("Business Operations - Data updated")

    # ===== MEMBERS & COMMUNICATION TAB =====
    def setup_members_communication(self):
        """Setup Members & Communication tab"""
        ttk.Label(self.members_frame, text="Members & Communication Center",
                 font=("Arial", 20, "bold")).pack(pady=20)

        # Communication options
        comm_frame = ttk.LabelFrame(self.members_frame, text="Quick Communication", padding=15)
        comm_frame.pack(fill=tk.X, padx=20, pady=10)

        # Create grid for communication buttons
        comm_grid = ttk.Frame(comm_frame)
        comm_grid.pack(fill=tk.X)

        for i in range(2):
            comm_grid.columnconfigure(i, weight=1)

        ttk.Button(comm_grid, text="💬 Message Wine Officer",
                  command=lambda: self.open_messaging_window("Wine Officer", "<EMAIL>")).grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(comm_grid, text="💬 Message Property Officer",
                  command=lambda: self.open_messaging_window("Property Officer", "<EMAIL>")).grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ttk.Button(comm_grid, text="💬 Message Financial Member",
                  command=lambda: self.open_messaging_window("Financial Member", "<EMAIL>")).grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ttk.Button(comm_grid, text="📢 Send to All Honorary Members",
                  command=self.message_all_honorary_members).grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Recent Messages
        messages_frame = ttk.LabelFrame(self.members_frame, text="Recent Messages", padding=15)
        messages_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Messages table
        messages_tree = ttk.Treeview(messages_frame, columns=("Time", "From", "To", "Subject", "Status"), show="headings", height=8)
        messages_tree.heading("Time", text="Time")
        messages_tree.heading("From", text="From")
        messages_tree.heading("To", text="To")
        messages_tree.heading("Subject", text="Subject")
        messages_tree.heading("Status", text="Status")

        messages_tree.column("Time", width=100)
        messages_tree.column("From", width=150)
        messages_tree.column("To", width=150)
        messages_tree.column("Subject", width=250)
        messages_tree.column("Status", width=100)

        # Sample messages data
        messages_data = [
            ("11:30 AM", "CSO", "Wine Officer", "Bar inventory approval needed", "✅ Read"),
            ("10:45 AM", "Wine Officer", "CSO", "Daily sales report submitted", "✅ Read"),
            ("10:15 AM", "CSO", "All HM Members", "Monthly mess meeting reminder", "📤 Sent"),
            ("09:30 AM", "Property Officer", "CSO", "Maintenance request approved", "✅ Read"),
            ("09:00 AM", "CSO", "Financial Member", "Monthly budget review", "⏳ Pending"),
            ("Yesterday", "CSO", "All Staff", "New safety protocols", "✅ Delivered")
        ]

        for msg in messages_data:
            messages_tree.insert("", tk.END, values=msg)

        messages_tree.pack(fill=tk.BOTH, expand=True, pady=10)

        # Message actions
        msg_actions_frame = ttk.Frame(self.members_frame)
        msg_actions_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(msg_actions_frame, text="📧 View All Messages",
                  command=self.view_all_messages).pack(side=tk.LEFT, padx=5)
        ttk.Button(msg_actions_frame, text="📝 Compose New Message",
                  command=self.compose_new_message).pack(side=tk.LEFT, padx=5)
        ttk.Button(msg_actions_frame, text="📊 Message Reports",
                  command=self.view_message_reports).pack(side=tk.LEFT, padx=5)

    def message_all_honorary_members(self):
        """Send message to all honorary members"""
        self.open_group_messaging_window("All Honorary Members", "<EMAIL>")

    def open_group_messaging_window(self, group_name, email):
        """Open messaging window for group communication"""
        msg_window = tk.Toplevel(self)
        msg_window.title(f"Message {group_name}")
        msg_window.geometry("600x500")
        msg_window.transient(self)
        msg_window.grab_set()

        # Header
        header_frame = ttk.Frame(msg_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text=f"Send Message to {group_name}",
                 font=("Arial", 16, "bold")).pack()
        ttk.Label(header_frame, text=f"This message will be sent to all members in this group",
                 font=("Arial", 10)).pack()

        # Message form
        form_frame = ttk.LabelFrame(msg_window, text="Message Details", padding=15)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Subject
        ttk.Label(form_frame, text="Subject:").pack(anchor=tk.W)
        subject_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=subject_var, width=60).pack(fill=tk.X, pady=5)

        # Message body
        ttk.Label(form_frame, text="Message:").pack(anchor=tk.W, pady=(10, 0))
        message_text = tk.Text(form_frame, height=12, width=60)
        message_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Options
        options_frame = ttk.Frame(form_frame)
        options_frame.pack(fill=tk.X, pady=10)

        ttk.Label(options_frame, text="Priority:").pack(side=tk.LEFT)
        priority_var = tk.StringVar(value="Normal")
        priority_combo = ttk.Combobox(options_frame, textvariable=priority_var, width=15)
        priority_combo['values'] = ('Low', 'Normal', 'High', 'Urgent')
        priority_combo.pack(side=tk.LEFT, padx=10)

        ttk.Label(options_frame, text="Delivery:").pack(side=tk.LEFT, padx=(20, 0))
        delivery_var = tk.StringVar(value="Email + SMS")
        delivery_combo = ttk.Combobox(options_frame, textvariable=delivery_var, width=15)
        delivery_combo['values'] = ('Email Only', 'SMS Only', 'Email + SMS', 'Notice Board')
        delivery_combo.pack(side=tk.LEFT, padx=10)

        # Buttons
        button_frame = ttk.Frame(msg_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Send to Group",
                  command=lambda: self.send_group_message(group_name, subject_var.get(),
                                                        message_text.get("1.0", tk.END),
                                                        priority_var.get(), delivery_var.get(), msg_window)).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel",
                  command=msg_window.destroy).pack(side=tk.RIGHT, padx=5)

    def send_group_message(self, group_name, subject, message, priority, delivery, window):
        """Send message to group"""
        if not subject.strip() or not message.strip():
            messagebox.showerror("Error", "Please fill in subject and message")
            return

        # Simulate member count based on group
        if "Honorary" in group_name:
            member_count = 45
        elif "Staff" in group_name:
            member_count = 25
        else:
            member_count = 15

        messagebox.showinfo("Group Message Sent",
                           f"Message sent successfully to {group_name}!\n\n"
                           f"Subject: {subject}\n"
                           f"Priority: {priority}\n"
                           f"Delivery Method: {delivery}\n"
                           f"Recipients: {member_count} members\n\n"
                           f"Message will be delivered according to selected method.")

        window.destroy()
        self.status_text.set(f"Group message sent to {group_name}")

    def view_all_messages(self):
        """View all messages in detail"""
        messagebox.showinfo("All Messages",
                           "Opening comprehensive message center...\n\n"
                           "Features:\n"
                           "• Inbox with all received messages\n"
                           "• Sent messages history\n"
                           "• Draft messages\n"
                           "• Message search and filtering\n"
                           "• Read receipts and delivery status")

    def compose_new_message(self):
        """Compose new message"""
        self.open_messaging_window("Select Recipient", "")

    def view_message_reports(self):
        """View message reports and statistics"""
        messagebox.showinfo("Message Reports",
                           "Message Communication Statistics:\n\n"
                           "This Month:\n"
                           "• Messages Sent: 156\n"
                           "• Messages Received: 89\n"
                           "• Group Messages: 23\n"
                           "• Announcements: 8\n\n"
                           "Response Rates:\n"
                           "• Wine Officer: 98% (avg 15 min)\n"
                           "• Property Officer: 95% (avg 25 min)\n"
                           "• Financial Member: 92% (avg 45 min)\n"
                           "• Honorary Members: 78% (avg 2 hours)")

    def refresh_members_communication(self):
        """Refresh members and communication data"""
        self.status_text.set("Members & Communication - Updated")

    def start_real_time_updates(self):
        """Start real-time updates for dashboard"""
        def update_dashboard():
            # Simulate new activities
            import random
            current_time = datetime.datetime.now().strftime("%H:%M")

            # Random activities that could happen
            possible_activities = [
                (current_time, "🍺 Bar Order: 3 beers sold to Table 5", "Completed"),
                (current_time, "🎂 Birthday reminder: Dr. Smith's birthday tomorrow", "Alert"),
                (current_time, "🏠 Room 104 booking inquiry received", "Pending"),
                (current_time, "💰 Payment received: ₦15,000 room booking", "Completed"),
                (current_time, "📢 Happy Hour announcement sent to all members", "Completed"),
                (current_time, "🍷 Wine inventory: 5 bottles restocked", "Completed"),
                (current_time, "📧 Message from Property Officer", "New"),
                (current_time, "🎉 Event reminder: Monthly dinner in 2 days", "Alert"),
                (current_time, "💳 Online payment processed successfully", "Completed"),
                (current_time, "📱 SMS notifications sent to 45 members", "Completed")
            ]

            # Add a random new activity every 30 seconds
            if random.random() < 0.3:  # 30% chance
                new_activity = random.choice(possible_activities)
                self.real_time_activities.insert(0, new_activity)

                # Keep only last 10 activities
                if len(self.real_time_activities) > 10:
                    self.real_time_activities = self.real_time_activities[:10]

                # Update display
                self.update_activities_display()

            # Schedule next update
            self.root.after(30000, update_dashboard)  # Update every 30 seconds

        # Start updates
        update_dashboard()

    def update_activities_display(self):
        """Update the activities display"""
        # Clear existing items
        for item in self.activities_list.get_children():
            self.activities_list.delete(item)

        # Add updated activities
        for activity in self.real_time_activities:
            self.activities_list.insert("", tk.END, values=activity)

    def add_real_time_activity(self, activity_text, source="System", amount=""):
        """Add a new real-time activity"""
        current_time = datetime.datetime.now().strftime("%H:%M")
        new_activity = (current_time, activity_text, source)

        self.real_time_activities.insert(0, new_activity)

        # Keep only last 10 activities
        if len(self.real_time_activities) > 10:
            self.real_time_activities = self.real_time_activities[:10]

        # Update display
        self.update_activities_display()

    def simulate_bar_sale(self, amount):
        """Simulate a bar sale and update dashboard"""
        self.add_real_time_activity(f"🍺 Bar Sale: ₦{amount:,} - Real-time update", "Wine Officer")

        # Update bar sales total (simulate)
        # In production, this would update actual financial data
        print(f"CSO Dashboard: Bar sale ₦{amount:,} recorded")

    def simulate_room_booking(self, room, guest_name, amount):
        """Simulate a room booking and update dashboard"""
        self.add_real_time_activity(f"🏠 Room {room} booked by {guest_name} - ₦{amount:,}", "Reception")

        # Update occupancy stats (simulate)
        print(f"CSO Dashboard: Room {room} booking ₦{amount:,} recorded")

    def simulate_social_announcement(self, announcement_type, title):
        """Simulate social secretary announcement"""
        self.add_real_time_activity(f"📢 {announcement_type}: {title}", "Social Secretary")

        # Update announcement stats (simulate)
        print(f"CSO Dashboard: {announcement_type} announcement sent")

    def open_online_booking_portal(self):
        """Open online booking portal"""
        try:
            import subprocess
            subprocess.Popen(["python", "online_booking_portal.py"])
            messagebox.showinfo("Online Portal", "Online booking portal opened in new window!")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open online portal: {str(e)}")

    def open_bar_ordering_system(self):
        """Open bar ordering system"""
        try:
            import subprocess
            subprocess.Popen(["python", "bar_ordering_system.py"])
            messagebox.showinfo("Bar System", "Bar ordering system opened in new window!")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open bar system: {str(e)}")

    def open_social_secretary_portal(self):
        """Open social secretary portal"""
        try:
            import subprocess
            subprocess.Popen(["python", "social_secretary_portal.py"])
            messagebox.showinfo("Social Portal", "Social secretary portal opened in new window!")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open social portal: {str(e)}")

    # ===== REPORTS & ANALYTICS TAB =====
    def setup_reports_analytics(self):
        """Setup Reports & Analytics tab"""
        ttk.Label(self.reports_frame, text="Reports & Analytics",
                 font=("Arial", 20, "bold")).pack(pady=20)

        # Report options
        reports_frame = ttk.LabelFrame(self.reports_frame, text="Available Reports", padding=15)
        reports_frame.pack(fill=tk.X, padx=20, pady=10)

        report_buttons = [
            ("Monthly P&L Statement", "Generate comprehensive profit & loss report"),
            ("Guest House Occupancy Report", "Detailed occupancy and revenue analysis"),
            ("Business Rent Collection Report", "Track rent payments from businesses"),
            ("Honorary Members Report", "Membership status and activities"),
            ("Cash Flow Analysis", "Monthly cash flow and projections")
        ]

        for title, desc in report_buttons:
            btn_frame = ttk.Frame(reports_frame)
            btn_frame.pack(fill=tk.X, pady=5)
            ttk.Button(btn_frame, text=title,
                      command=lambda t=title: messagebox.showinfo("Report", f"Generating {t}...")).pack(side=tk.LEFT)
            ttk.Label(btn_frame, text=f" - {desc}", font=("Arial", 9)).pack(side=tk.LEFT, padx=10)

    def refresh_reports_analytics(self):
        """Refresh reports and analytics"""
        self.status_text.set("Reports & Analytics - Updated")

    # ===== GUEST HOUSE CSO VIEW =====
    def setup_guest_house_cso_view(self):
        """Setup CSO-focused Guest House view"""
        ttk.Label(self.guest_house_frame, text="Guest House Management",
                 font=("Arial", 20, "bold")).pack(pady=20)

        # Quick stats
        stats_frame = ttk.Frame(self.guest_house_frame)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)

        for i in range(3):
            stats_frame.columnconfigure(i, weight=1)

        self.create_stat_card(stats_frame, 0, 0, "Occupancy Rate", "67% (4/6)", "info")
        self.create_stat_card(stats_frame, 0, 1, "Monthly Revenue", "₦450,000", "success")
        self.create_stat_card(stats_frame, 0, 2, "Pending Checkouts", "2 Today", "warning")

        # Room status overview
        rooms_frame = ttk.LabelFrame(self.guest_house_frame, text="Room Status Overview", padding=15)
        rooms_frame.pack(fill=tk.X, padx=20, pady=10)

        # Interactive Room Grid with Clickable Booking
        rooms_grid_frame = ttk.Frame(rooms_frame)
        rooms_grid_frame.pack(fill=tk.X, pady=10)

        # Configure grid
        for i in range(3):
            rooms_grid_frame.columnconfigure(i, weight=1)

        # Room data with status-based pricing
        room_data = [
            {"number": "101", "status": "Occupied", "guest": "Lt. Col. Johnson (Officer)", "rate": 10000, "checkout": "Tomorrow"},
            {"number": "102", "status": "Available", "guest": None, "rate": None, "checkout": None},
            {"number": "103", "status": "Occupied", "guest": "Maj. Williams (HM)", "rate": 10000, "checkout": "Today"},
            {"number": "104", "status": "Available", "guest": None, "rate": None, "checkout": None},
            {"number": "105", "status": "Occupied", "guest": "Cdr. Smith (HM)", "rate": 10000, "checkout": "2 days"},
            {"number": "106", "status": "Occupied", "guest": "Mr. Brown (Guest)", "rate": 15000, "checkout": "3 days"}
        ]

        # Create clickable room cards
        for i, room in enumerate(room_data):
            row = i // 3
            col = i % 3

            # Room card frame
            if room["status"] == "Available":
                card_style = "success"  # Green for available
                card_text = f"Room {room['number']}\n✅ AVAILABLE\n\nClick to Book"
                button_state = "normal"
            else:
                card_style = "secondary"  # Gray for occupied
                card_text = f"Room {room['number']}\n🔴 OCCUPIED\n{room['guest']}\nCheckout: {room['checkout']}"
                button_state = "disabled"

            room_card = ttk.Button(rooms_grid_frame, text=card_text,
                                  command=lambda r=room: self.open_room_booking(r),
                                  state=button_state)
            room_card.grid(row=row, column=col, padx=5, pady=5, sticky="nsew", ipadx=20, ipady=20)

            # Add hover tooltip for available rooms
            if room["status"] == "Available":
                self.create_room_tooltip(room_card, room["number"])

        # Configure row weights
        for i in range((len(room_data) + 2) // 3):
            rooms_grid_frame.rowconfigure(i, weight=1)

        # Quick actions
        actions_frame = ttk.Frame(self.guest_house_frame)
        actions_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(actions_frame, text="📊 Detailed Guest House Report",
                  command=lambda: messagebox.showinfo("Report", "Opening detailed guest house report...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="💰 Revenue Analysis",
                  command=lambda: messagebox.showinfo("Revenue", "Opening revenue analysis...")).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="📋 Booking Management",
                  command=lambda: messagebox.showinfo("Booking", "Opening booking management...")).pack(side=tk.LEFT, padx=5)

    def refresh_guest_house_cso_view(self):
        """Refresh CSO guest house view"""
        self.status_text.set("Guest House - CSO view updated")

    def create_room_tooltip(self, widget, room_number):
        """Create hover tooltip for room booking"""
        def show_tooltip(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            tooltip_text = f"""Room {room_number} - Available for Booking

PRICING STRUCTURE:
• Honorary Mess Members: ₦10,000/night
• Military Officers: ₦10,000/night
• General Guests: ₦15,000/night

BENEFITS OF HONORARY MEMBERSHIP:
✅ Reduced accommodation rates
✅ Access to mess facilities
✅ Networking with military officers
✅ Exclusive events and functions
✅ Professional development opportunities

Click to book this room or inquire about Honorary Membership!"""

            label = tk.Label(tooltip, text=tooltip_text,
                           background="lightyellow",
                           relief="solid",
                           borderwidth=1,
                           font=("Arial", 10),
                           justify="left")
            label.pack()

            def hide_tooltip():
                tooltip.destroy()

            tooltip.after(5000, hide_tooltip)  # Auto-hide after 5 seconds

        widget.bind("<Enter>", show_tooltip)

    def open_room_booking(self, room):
        """Open room booking dialog with status-based pricing"""
        booking_window = tk.Toplevel(self)
        booking_window.title(f"Book Room {room['number']}")
        booking_window.geometry("500x600")
        booking_window.transient(self)
        booking_window.grab_set()

        # Header
        header_frame = ttk.Frame(booking_window)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(header_frame, text=f"Room {room['number']} Booking",
                 font=("Arial", 16, "bold")).pack()
        ttk.Label(header_frame, text="Please provide your details for booking",
                 font=("Arial", 10)).pack()

        # Guest details form
        form_frame = ttk.LabelFrame(booking_window, text="Guest Information", padding=15)
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        # Guest status selection
        ttk.Label(form_frame, text="Your Status:").grid(row=0, column=0, sticky=tk.W, pady=5)
        status_var = tk.StringVar()
        status_combo = ttk.Combobox(form_frame, textvariable=status_var, width=25)
        status_combo['values'] = ('Honorary Mess Member', 'Military Officer', 'General Guest')
        status_combo.grid(row=0, column=1, pady=5, sticky=tk.W)
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.update_room_rate(status_var.get(), rate_label))

        # Name
        ttk.Label(form_frame, text="Full Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=name_var, width=30).grid(row=1, column=1, pady=5, sticky=tk.W)

        # Rank/Title
        ttk.Label(form_frame, text="Rank/Title:").grid(row=2, column=0, sticky=tk.W, pady=5)
        rank_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=rank_var, width=30).grid(row=2, column=1, pady=5, sticky=tk.W)

        # Phone
        ttk.Label(form_frame, text="Phone Number:").grid(row=3, column=0, sticky=tk.W, pady=5)
        phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=phone_var, width=30).grid(row=3, column=1, pady=5, sticky=tk.W)

        # Check-out date
        ttk.Label(form_frame, text="Check-out Date:").grid(row=4, column=0, sticky=tk.W, pady=5)
        checkout_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=checkout_var, width=30).grid(row=4, column=1, pady=5, sticky=tk.W)
        ttk.Label(form_frame, text="(YYYY-MM-DD)").grid(row=4, column=2, sticky=tk.W, pady=5)

        # Rate display
        rate_frame = ttk.LabelFrame(booking_window, text="Booking Summary", padding=15)
        rate_frame.pack(fill=tk.X, padx=20, pady=10)

        rate_label = ttk.Label(rate_frame, text="Please select your status to see rate",
                              font=("Arial", 12, "bold"))
        rate_label.pack()

        # Honorary membership info
        info_frame = ttk.LabelFrame(booking_window, text="💡 Honorary Membership Benefits", padding=15)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        benefits_text = """Join as Honorary Mess Member and enjoy:
• 33% savings on accommodation (₦10,000 vs ₦15,000)
• Access to exclusive mess events and networking
• Professional development opportunities
• Lifetime membership benefits"""

        ttk.Label(info_frame, text=benefits_text, justify=tk.LEFT).pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(booking_window)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Button(button_frame, text="📋 Apply for Honorary Membership",
                  command=lambda: self.open_honorary_membership_application()).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="✅ Confirm Booking",
                  command=lambda: self.confirm_room_booking(room, status_var.get(), name_var.get(),
                                                          rank_var.get(), phone_var.get(), checkout_var.get(),
                                                          booking_window)).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="❌ Cancel",
                  command=booking_window.destroy).pack(side=tk.RIGHT, padx=5)

    def update_room_rate(self, status, rate_label):
        """Update room rate based on guest status"""
        if status in ['Honorary Mess Member', 'Military Officer']:
            rate = 10000
            savings_text = "\n💰 You save ₦5,000 per night!"
        else:
            rate = 15000
            savings_text = "\n💡 Consider Honorary Membership to save ₦5,000/night"

        rate_text = f"Room Rate: ₦{rate:,}/night{savings_text}"
        rate_label.config(text=rate_text)

    def confirm_room_booking(self, room, status, name, rank, phone, checkout_date, window):
        """Confirm room booking with proper validation"""
        if not all([status, name, phone, checkout_date]):
            messagebox.showerror("Error", "Please fill in all required fields")
            return

        # Calculate rate based on status
        rate = 10000 if status in ['Honorary Mess Member', 'Military Officer'] else 15000

        # Here you would integrate with the actual booking system
        messagebox.showinfo("Booking Confirmed",
                           f"Room {room['number']} booked successfully!\n\n"
                           f"Guest: {name} ({status})\n"
                           f"Rate: ₦{rate:,}/night\n"
                           f"Check-out: {checkout_date}\n\n"
                           f"Booking confirmation will be sent to your phone.")

        window.destroy()
        # Refresh the guest house view
        self.refresh_guest_house_cso_view()

    def open_honorary_membership_application(self):
        """Open honorary membership application"""
        messagebox.showinfo("Honorary Membership",
                           "Opening Honorary Membership Application...\n\n"
                           "Benefits include:\n"
                           "• Reduced accommodation rates\n"
                           "• Access to mess facilities\n"
                           "• Networking opportunities\n"
                           "• Exclusive events")
        # This would open the honorary membership portal

    # ===== LEGACY INVENTORY TAB (kept for reference) =====
    def setup_inventory_tab(self):
        """Setup the inventory management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.inventory_frame, text="Item Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.inventory_frame, text="Inventory Items")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        qr_frame = ttk.LabelFrame(self.inventory_frame, text="QR Code")
        qr_frame.pack(fill=tk.Y, side=tk.RIGHT, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Item Name:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.item_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.item_name_var, width=20).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Category:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(form_frame, textvariable=self.category_var, width=15)
        category_combo['values'] = ('Food', 'Beverages', 'Electronics', 'Clothing', 'Household', 'General')
        category_combo.grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Supplier:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.supplier_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.supplier_var, width=20).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Price:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.price_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.price_var, width=15).grid(row=1, column=3, padx=5, pady=5)

        # Row 2
        ttk.Label(form_frame, text="Quantity:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.quantity_var = tk.StringVar()
        self.quantity_var.set("1")
        ttk.Entry(form_frame, textvariable=self.quantity_var, width=10).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(form_frame, text="Expiration Date:").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        self.expiry_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.expiry_var, width=15).grid(row=2, column=3, padx=5, pady=5)
        ttk.Label(form_frame, text="(YYYY-MM-DD)").grid(row=2, column=4, padx=0, pady=5, sticky=tk.W)

        # Row 3
        ttk.Label(form_frame, text="Notes:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.notes_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.notes_var, width=40).grid(row=3, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W+tk.E)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=4, column=0, columnspan=5, pady=10)

        ttk.Button(button_frame, text="Add Item", command=self.add_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Update", command=self.update_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete", command=self.delete_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_inventory_form).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Generate QR", command=self.generate_item_qr).pack(side=tk.LEFT, padx=5)

        # Search frame
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        self.inventory_search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.inventory_search_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Search", command=self.search_inventory).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Show All", command=self.refresh_inventory).pack(side=tk.LEFT, padx=5)

        # Filter by category
        ttk.Label(search_frame, text="Filter by Category:").pack(side=tk.LEFT, padx=5)
        self.filter_category_var = tk.StringVar()
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_category_var, width=15)
        filter_combo['values'] = ('All', 'Food', 'Beverages', 'Electronics', 'Clothing', 'Household', 'General')
        filter_combo.current(0)
        filter_combo.pack(side=tk.LEFT, padx=5)
        filter_combo.bind("<<ComboboxSelected>>", lambda e: self.filter_inventory_by_category())

        # Inventory list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.inventory_tree = ttk.Treeview(tree_frame, columns=("ID", "Name", "Category", "Supplier", "Price", "Quantity", "Expiry"), show="headings")

        # Define headings
        self.inventory_tree.heading("ID", text="ID")
        self.inventory_tree.heading("Name", text="Item Name")
        self.inventory_tree.heading("Category", text="Category")
        self.inventory_tree.heading("Supplier", text="Supplier")
        self.inventory_tree.heading("Price", text="Price")
        self.inventory_tree.heading("Quantity", text="Qty")
        self.inventory_tree.heading("Expiry", text="Expiration Date")

        # Define columns
        self.inventory_tree.column("ID", width=50)
        self.inventory_tree.column("Name", width=150)
        self.inventory_tree.column("Category", width=100)
        self.inventory_tree.column("Supplier", width=120)
        self.inventory_tree.column("Price", width=80)
        self.inventory_tree.column("Quantity", width=60)
        self.inventory_tree.column("Expiry", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.inventory_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.inventory_tree.bind("<<TreeviewSelect>>", self.on_inventory_select)

        # QR code display
        self.qr_label = ttk.Label(qr_frame, text="Select an item to display QR code")
        self.qr_label.pack(padx=10, pady=10)

        # Initial data load
        self.refresh_inventory()

    def refresh_inventory(self):
        """Refresh the inventory list"""
        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Get all inventory items
        items = db.fetch_inventory()

        # Add to treeview
        for item in items:
            # item format: id, name, category, supplier, price, quantity, expiry_date, purchase_date, qr_code, notes
            self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def on_inventory_select(self, event):
        """Handle inventory item selection"""
        selected_item = self.inventory_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.inventory_tree.item(selected_item[0])
        values = item['values']

        # Fill the form fields
        self.item_name_var.set(values[1])
        self.category_var.set(values[2])
        self.supplier_var.set(values[3])
        self.price_var.set(values[4])
        self.quantity_var.set(values[5])
        self.expiry_var.set(values[6] if values[6] else "")

        # Get full item details including notes and QR code
        self.cur_selected_id = values[0]

        # Display QR code if available
        # This would need to be implemented to fetch the QR code from the database
        self.display_qr_code(self.cur_selected_id)

    def add_inventory_item(self):
        """Add a new inventory item"""
        # Validate inputs
        if not self.validate_inventory_form():
            return

        try:
            # Get form values
            name = self.item_name_var.get()
            category = self.category_var.get()
            supplier = self.supplier_var.get()
            price = float(self.price_var.get())
            quantity = int(self.quantity_var.get())
            expiry = self.expiry_var.get() if self.expiry_var.get() else None
            notes = self.notes_var.get()

            # Add to database
            db.insert_inventory(name, category, supplier, price, quantity, expiry, notes)

            # Refresh the list
            self.refresh_inventory()

            # Clear the form
            self.clear_inventory_form()

            # Show success message
            self.status_text.set(f"Item '{name}' added to inventory")
            messagebox.showinfo("Success", f"Item '{name}' added to inventory")

        except Exception as e:
            messagebox.showerror("Error", f"Error adding item: {str(e)}")

    def update_inventory_item(self):
        """Update an existing inventory item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to update")
            return

        # Validate inputs
        if not self.validate_inventory_form():
            return

        try:
            # Get form values
            name = self.item_name_var.get()
            category = self.category_var.get()
            supplier = self.supplier_var.get()
            price = float(self.price_var.get())
            quantity = int(self.quantity_var.get())
            expiry = self.expiry_var.get() if self.expiry_var.get() else None
            notes = self.notes_var.get()

            # Update in database
            db.update_inventory(self.cur_selected_id, name, category, supplier, price, quantity, expiry, notes)

            # Refresh the list
            self.refresh_inventory()

            # Show success message
            self.status_text.set(f"Item '{name}' updated")
            messagebox.showinfo("Success", f"Item '{name}' updated")

        except Exception as e:
            messagebox.showerror("Error", f"Error updating item: {str(e)}")

    def delete_inventory_item(self):
        """Delete an inventory item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this item?"):
            return

        try:
            # Delete from database
            db.remove_inventory(self.cur_selected_id)

            # Refresh the list
            self.refresh_inventory()

            # Clear the form
            self.clear_inventory_form()

            # Show success message
            self.status_text.set("Item deleted from inventory")

        except Exception as e:
            messagebox.showerror("Error", f"Error deleting item: {str(e)}")

    def clear_inventory_form(self):
        """Clear the inventory form fields"""
        self.item_name_var.set("")
        self.category_var.set("")
        self.supplier_var.set("")
        self.price_var.set("")
        self.quantity_var.set("1")
        self.expiry_var.set("")
        self.notes_var.set("")
        if hasattr(self, 'cur_selected_id'):
            delattr(self, 'cur_selected_id')

        # Clear QR code display
        self.qr_label.config(text="Select an item to display QR code", image="")

    def validate_inventory_form(self):
        """Validate inventory form inputs"""
        if not self.item_name_var.get():
            messagebox.showwarning("Validation Error", "Item name is required")
            return False

        if not self.price_var.get():
            messagebox.showwarning("Validation Error", "Price is required")
            return False

        try:
            price = float(self.price_var.get())
            if price < 0:
                messagebox.showwarning("Validation Error", "Price must be a positive number")
                return False
        except ValueError:
            messagebox.showwarning("Validation Error", "Price must be a number")
            return False

        try:
            quantity = int(self.quantity_var.get())
            if quantity < 0:
                messagebox.showwarning("Validation Error", "Quantity must be a positive integer")
                return False
        except ValueError:
            messagebox.showwarning("Validation Error", "Quantity must be an integer")
            return False

        # Validate expiration date format if provided
        if self.expiry_var.get():
            try:
                datetime.datetime.strptime(self.expiry_var.get(), "%Y-%m-%d")
            except ValueError:
                messagebox.showwarning("Validation Error", "Expiration date must be in YYYY-MM-DD format")
                return False

        return True

    def search_inventory(self):
        """Search inventory items"""
        search_term = self.inventory_search_var.get().strip().lower()
        if not search_term:
            self.refresh_inventory()
            return

        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Get all inventory items
        items = db.fetch_inventory()

        # Filter and add to treeview
        for item in items:
            # Check if search term is in any of the relevant fields
            if (search_term in str(item[1]).lower() or  # name
                search_term in str(item[2]).lower() or  # category
                search_term in str(item[3]).lower()):   # supplier

                self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def filter_inventory_by_category(self):
        """Filter inventory by selected category"""
        category = self.filter_category_var.get()

        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        if category == "All":
            self.refresh_inventory()
            return

        # Get filtered inventory items
        items = db.fetch_inventory(category)

        # Add to treeview
        for item in items:
            self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def generate_item_qr(self):
        """Generate QR code for the current item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to generate QR code")
            return

        # This would need to be implemented to generate and save QR code to the database
        # For now, just display a sample QR code
        self.display_qr_code(self.cur_selected_id)

        self.status_text.set("QR code generated")

    def display_qr_code(self, item_id):
        """Display QR code for an item"""
        # This is a placeholder. In a real implementation, you would fetch the QR code from the database
        # For demonstration, we'll generate a simple QR code
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(f"ITEM:{item_id}")
            qr.make(fit=True)
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to PhotoImage
            img = img.resize((200, 200))
            photo_img = ImageTk.PhotoImage(img)

            # Update label
            self.qr_label.config(image=photo_img, text="")
            self.qr_label.image = photo_img  # Keep a reference

        except Exception as e:
            self.qr_label.config(text=f"Error displaying QR code: {str(e)}", image="")

    # ===== RESTAURANT TAB =====
    def setup_restaurant_tab(self):
        """Setup the restaurant management tab"""
        # Create notebook for restaurant sub-tabs
        restaurant_notebook = ttk.Notebook(self.restaurant_frame)
        restaurant_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create sub-tabs
        self.menu_frame = ttk.Frame(restaurant_notebook)
        self.orders_frame = ttk.Frame(restaurant_notebook)

        restaurant_notebook.add(self.menu_frame, text="Menu Management")
        restaurant_notebook.add(self.orders_frame, text="Orders")

        # Setup each sub-tab
        self.setup_menu_tab()
        self.setup_orders_tab()

    def setup_menu_tab(self):
        """Setup the menu management tab"""
        # Placeholder for now
        ttk.Label(self.menu_frame, text="Restaurant Menu Management - To be implemented").pack(pady=20)

    def setup_orders_tab(self):
        """Setup the orders management tab"""
        # Placeholder for now
        ttk.Label(self.orders_frame, text="Restaurant Orders Management - To be implemented").pack(pady=20)

    def refresh_restaurants(self):
        """Refresh restaurant data"""
        # Placeholder for now
        pass

    # ===== GYM TAB =====
    def setup_gym_tab(self):
        """Setup the gym management tab"""
        # Placeholder for now
        ttk.Label(self.gym_frame, text="Gym Membership Management - To be implemented").pack(pady=20)

    def refresh_gym(self):
        """Refresh gym data"""
        # Placeholder for now
        pass

    # ===== GUEST HOUSE TAB =====
    def setup_guest_house_tab(self):
        """Setup the guest house management tab"""
        # Initialize guest house rooms if needed
        db.initialize_guest_house_rooms()

        # Create notebook for guest house sub-tabs
        guest_house_notebook = ttk.Notebook(self.guest_house_frame)
        guest_house_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create sub-tabs
        self.room_dashboard_frame = ttk.Frame(guest_house_notebook)
        self.booking_frame = ttk.Frame(guest_house_notebook)
        self.orders_frame = ttk.Frame(guest_house_notebook)

        guest_house_notebook.add(self.room_dashboard_frame, text="Room Dashboard")
        guest_house_notebook.add(self.booking_frame, text="Bookings")
        guest_house_notebook.add(self.orders_frame, text="Orders")

        # Setup each sub-tab
        self.setup_room_dashboard()
        self.setup_booking_tab()
        self.setup_orders_tab()

    def setup_room_dashboard(self):
        """Setup the room dashboard tab"""
        # Create a frame for the room cards
        self.rooms_frame = ttk.Frame(self.room_dashboard_frame)
        self.rooms_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a title
        ttk.Label(self.rooms_frame, text="Guest House Room Status", font=("Arial", 16, "bold")).pack(pady=10)

        # Create a frame for the room cards
        self.room_cards_frame = ttk.Frame(self.rooms_frame)
        self.room_cards_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Refresh the room dashboard
        self.refresh_room_dashboard()

    def setup_booking_tab(self):
        """Setup the booking management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.booking_frame, text="Booking Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.booking_frame, text="Booking List")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Customer:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_customer_var = tk.StringVar()
        self.booking_customer_combo = ttk.Combobox(form_frame, textvariable=self.booking_customer_var, width=30)
        self.booking_customer_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Room:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_room_var = tk.StringVar()
        self.booking_room_combo = ttk.Combobox(form_frame, textvariable=self.booking_room_var, width=20)
        self.booking_room_combo.grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Check-in Date:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_checkin_var = tk.StringVar()
        self.booking_checkin_var.set(datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(form_frame, textvariable=self.booking_checkin_var, width=15, state="readonly").grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Check-out Date:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_checkout_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.booking_checkout_var, width=15).grid(row=1, column=3, padx=5, pady=5)
        ttk.Label(form_frame, text="(YYYY-MM-DD)").grid(row=1, column=4, padx=0, pady=5, sticky=tk.W)

        # Row 2
        ttk.Label(form_frame, text="ID Card:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_idcard_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.booking_idcard_var, width=20).grid(row=2, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Meal Plan:").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_meal_var = tk.StringVar()
        meal_combo = ttk.Combobox(form_frame, textvariable=self.booking_meal_var, width=15)
        meal_combo['values'] = ('None', 'Breakfast Only', 'Half Board', 'Full Board')
        meal_combo.current(0)
        meal_combo.grid(row=2, column=3, padx=5, pady=5)

        # Row 3 - Companions
        ttk.Label(form_frame, text="Companions:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.has_companions_var = tk.BooleanVar()
        self.has_companions_var.set(False)
        ttk.Checkbutton(form_frame, text="Guest has companions", variable=self.has_companions_var,
                       command=self.toggle_companions_frame).grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)

        # Companions frame (initially hidden)
        self.companions_frame = ttk.LabelFrame(form_frame, text="Companion Details")
        self.companions_frame.grid(row=4, column=0, columnspan=5, padx=5, pady=5, sticky=tk.W+tk.E)
        self.companions_frame.grid_remove()  # Hide initially

        # Companion list
        self.companions_list = []

        # Add companion button
        ttk.Button(self.companions_frame, text="Add Companion", command=self.add_companion_dialog).pack(pady=5)

        # Companions listbox
        self.companions_listbox = tk.Listbox(self.companions_frame, width=60, height=4)
        self.companions_listbox.pack(fill=tk.X, padx=5, pady=5)

        # Remove companion button
        ttk.Button(self.companions_frame, text="Remove Selected Companion",
                  command=self.remove_companion).pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=5, column=0, columnspan=5, pady=10)

        ttk.Button(button_frame, text="Book Room", command=self.book_room).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Check Out", command=self.check_out_guest).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_booking_form).pack(side=tk.LEFT, padx=5)

        # Booking list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.booking_tree = ttk.Treeview(tree_frame, columns=("ID", "Room", "Customer", "Check-in", "Check-out", "Status", "Total"), show="headings")

        # Define headings
        self.booking_tree.heading("ID", text="ID")
        self.booking_tree.heading("Room", text="Room")
        self.booking_tree.heading("Customer", text="Customer")
        self.booking_tree.heading("Check-in", text="Check-in Date")
        self.booking_tree.heading("Check-out", text="Check-out Date")
        self.booking_tree.heading("Status", text="Status")
        self.booking_tree.heading("Total", text="Total Amount")

        # Define columns
        self.booking_tree.column("ID", width=50)
        self.booking_tree.column("Room", width=100)
        self.booking_tree.column("Customer", width=150)
        self.booking_tree.column("Check-in", width=100)
        self.booking_tree.column("Check-out", width=100)
        self.booking_tree.column("Status", width=100)
        self.booking_tree.column("Total", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.booking_tree.yview)
        self.booking_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.booking_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.booking_tree.bind("<<TreeviewSelect>>", self.on_booking_select)

        # Load customer and room data
        self.load_customer_data()
        self.load_room_data()

        # Initial data load
        self.refresh_bookings()

    def setup_orders_tab(self):
        """Setup the orders management tab"""
        # Placeholder for now
        ttk.Label(self.orders_frame, text="Guest House Orders - To be implemented").pack(pady=20)

    def refresh_guest_house(self):
        """Refresh all guest house data"""
        self.refresh_room_dashboard()
        self.refresh_bookings()

    def refresh_bookings(self):
        """Refresh the booking list"""
        # Clear the treeview
        for item in self.booking_tree.get_children():
            self.booking_tree.delete(item)

        # Get all bookings with room and customer info
        self.cur.execute("""
            SELECT b.id, g.room_number, c.name, b.check_in_date, b.check_out_date,
                   b.status, b.total_amount
            FROM guest_house_bookings b
            JOIN guest_house g ON b.room_id = g.id
            JOIN customers c ON b.customer_id = c.id
            ORDER BY b.check_in_date DESC
        """)

        bookings = self.cur.fetchall()

        # Add to treeview
        for booking in bookings:
            self.booking_tree.insert("", tk.END, values=booking)

    def on_booking_select(self, event):
        """Handle booking selection"""
        selected_item = self.booking_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.booking_tree.item(selected_item[0])
        values = item['values']

        # Store the selected booking ID
        self.cur_selected_booking_id = values[0]

        # Get room and customer IDs for the booking
        self.cur.execute("""
            SELECT room_id, customer_id, check_out_date, id_card, meal_plan
            FROM guest_house_bookings
            WHERE id = ?
        """, (self.cur_selected_booking_id,))

        booking_details = self.cur.fetchone()
        if booking_details:
            room_id, customer_id, check_out_date, id_card, meal_plan = booking_details

            # Set the form fields
            # Find the customer in the combobox
            self.cur.execute("SELECT name FROM customers WHERE id = ?", (customer_id,))
            customer_name = self.cur.fetchone()[0]
            customer_index = -1
            for i, value in enumerate(self.booking_customer_combo['values']):
                if value.startswith(f"{customer_id} - "):
                    customer_index = i
                    break
            if customer_index >= 0:
                self.booking_customer_combo.current(customer_index)

            # Find the room in the combobox
            self.cur.execute("SELECT room_number, room_type FROM guest_house WHERE id = ?", (room_id,))
            room_info = self.cur.fetchone()
            if room_info:
                room_number, room_type = room_info
                room_index = -1
                for i, value in enumerate(self.booking_room_combo['values']):
                    if value.startswith(f"{room_number} - "):
                        room_index = i
                        break
                if room_index >= 0:
                    self.booking_room_combo.current(room_index)

            # Set other fields
            self.booking_checkout_var.set(check_out_date)
            self.booking_idcard_var.set(id_card if id_card else "")

            # Set meal plan
            if meal_plan:
                meal_index = 0
                meal_options = self.booking_meal_var.get()
                for i, option in enumerate(['None', 'Breakfast Only', 'Half Board', 'Full Board']):
                    if option == meal_plan:
                        meal_index = i
                        break
                self.booking_meal_var.set(meal_plan)

    def toggle_companions_frame(self):
        """Show or hide the companions frame based on checkbox state"""
        if self.has_companions_var.get():
            self.companions_frame.grid()
        else:
            self.companions_frame.grid_remove()

    def add_companion_dialog(self):
        """Open dialog to add a companion"""
        # Create a dialog window
        dialog = tk.Toplevel(self)
        dialog.title("Add Companion")
        dialog.geometry("400x300")
        dialog.transient(self)
        dialog.grab_set()  # Make dialog modal

        # Create form fields
        form_frame = ttk.Frame(dialog, padding=10)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Name field
        ttk.Label(form_frame, text="Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        # Relationship field
        ttk.Label(form_frame, text="Relationship:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        relationship_var = tk.StringVar()
        relationship_combo = ttk.Combobox(form_frame, textvariable=relationship_var, width=20)
        relationship_combo['values'] = ('Spouse', 'Child', 'Parent', 'Sibling', 'Friend', 'Colleague', 'Other')
        relationship_combo.grid(row=1, column=1, padx=5, pady=5)

        # ID Card field
        ttk.Label(form_frame, text="ID Card:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        id_card_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=id_card_var, width=20).grid(row=2, column=1, padx=5, pady=5)

        # Phone field
        ttk.Label(form_frame, text="Phone:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=phone_var, width=20).grid(row=3, column=1, padx=5, pady=5)

        # Add button
        def add_companion():
            name = name_var.get()
            if not name:
                messagebox.showwarning("Validation Error", "Companion name is required", parent=dialog)
                return

            # Create companion dictionary
            companion = {
                'name': name,
                'relationship': relationship_var.get(),
                'id_card': id_card_var.get(),
                'phone': phone_var.get()
            }

            # Add to list and update listbox
            self.companions_list.append(companion)
            self.update_companions_listbox()

            # Close dialog
            dialog.destroy()

        ttk.Button(form_frame, text="Add Companion", command=add_companion).grid(row=4, column=0, columnspan=2, pady=10)

        # Cancel button
        ttk.Button(form_frame, text="Cancel", command=dialog.destroy).grid(row=5, column=0, columnspan=2, pady=5)

    def update_companions_listbox(self):
        """Update the companions listbox with current companions"""
        self.companions_listbox.delete(0, tk.END)
        for companion in self.companions_list:
            display_text = f"{companion['name']} - {companion['relationship'] if companion['relationship'] else 'N/A'}"
            self.companions_listbox.insert(tk.END, display_text)

    def remove_companion(self):
        """Remove the selected companion from the list"""
        selected_index = self.companions_listbox.curselection()
        if not selected_index:
            messagebox.showwarning("Selection Required", "Please select a companion to remove")
            return

        # Remove from list and update listbox
        self.companions_list.pop(selected_index[0])
        self.update_companions_listbox()

    def book_room(self):
        """Book a room"""
        # Validate inputs
        if not self.validate_booking_form():
            return

        try:
            # Parse customer ID from combobox
            customer_str = self.booking_customer_var.get()
            customer_id = int(customer_str.split(' - ')[0])

            # Parse room ID from combobox
            room_str = self.booking_room_var.get()
            room_number = room_str.split(' - ')[0]

            # Get room ID from room number
            self.cur.execute("SELECT id, status, capacity FROM guest_house WHERE room_number = ?", (room_number,))
            room_info = self.cur.fetchone()

            if not room_info:
                messagebox.showwarning("Room Not Found", "The selected room was not found.")
                return

            room_id, status, capacity = room_info

            if status != "Available":
                messagebox.showwarning("Room Not Available", "The selected room is not available for booking.")
                return

            # Check if companions exceed room capacity
            companion_count = len(self.companions_list)
            if self.has_companions_var.get() and companion_count + 1 > capacity:
                messagebox.showwarning("Capacity Exceeded",
                                      f"Room capacity ({capacity}) exceeded with {companion_count + 1} guests.")
                return

            # Get form values
            check_out_date = self.booking_checkout_var.get()
            id_card = self.booking_idcard_var.get()
            meal_plan = self.booking_meal_var.get() if self.booking_meal_var.get() != "None" else None

            # Get companions if any
            companions = self.companions_list if self.has_companions_var.get() else None

            # Book the room
            booking_id = db.book_guest_house(customer_id, room_id, check_out_date, id_card, None, meal_plan, companions)

            # Refresh the data
            self.refresh_guest_house()

            # Clear the form
            self.clear_booking_form()

            # Show success message
            guest_text = f" with {companion_count} companions" if companions else ""
            self.status_text.set(f"Room {room_number} booked successfully{guest_text}")
            messagebox.showinfo("Success", f"Room {room_number} booked successfully{guest_text}")

        except Exception as e:
            messagebox.showerror("Error", f"Error booking room: {str(e)}")

    def check_out_guest(self):
        """Check out a guest"""
        if not hasattr(self, 'cur_selected_booking_id') or not self.cur_selected_booking_id:
            messagebox.showwarning("Selection Required", "Please select a booking to check out")
            return

        # Confirm check-out
        if not messagebox.askyesno("Confirm Check-out", "Are you sure you want to check out this guest?"):
            return

        try:
            # Get room ID for the booking
            self.cur.execute("SELECT room_id FROM guest_house_bookings WHERE id = ?", (self.cur_selected_booking_id,))
            room_id = self.cur.fetchone()[0]

            # Update booking status
            self.cur.execute("UPDATE guest_house_bookings SET status = 'Completed' WHERE id = ?", (self.cur_selected_booking_id,))

            # Update room status
            self.cur.execute("UPDATE guest_house SET status = 'Available' WHERE id = ?", (room_id,))

            self.conn.commit()

            # Refresh the data
            self.refresh_guest_house()

            # Clear the form
            self.clear_booking_form()

            # Show success message
            self.status_text.set("Guest checked out successfully")
            messagebox.showinfo("Success", "Guest checked out successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Error checking out guest: {str(e)}")

    def clear_booking_form(self):
        """Clear the booking form fields"""
        if len(self.booking_customer_combo['values']) > 0:
            self.booking_customer_combo.current(0)
        if len(self.booking_room_combo['values']) > 0:
            self.booking_room_combo.current(0)
        self.booking_checkin_var.set(datetime.datetime.now().strftime("%Y-%m-%d"))
        self.booking_checkout_var.set("")
        self.booking_idcard_var.set("")
        self.booking_meal_var.set("None")

        # Clear companions
        self.has_companions_var.set(False)
        self.companions_list = []
        self.companions_listbox.delete(0, tk.END)
        self.companions_frame.grid_remove()

        if hasattr(self, 'cur_selected_booking_id'):
            delattr(self, 'cur_selected_booking_id')

    def validate_booking_form(self):
        """Validate booking form inputs"""
        if not self.booking_customer_var.get():
            messagebox.showwarning("Validation Error", "Please select a customer")
            return False

        if not self.booking_room_var.get():
            messagebox.showwarning("Validation Error", "Please select a room")
            return False

        if not self.booking_checkout_var.get():
            messagebox.showwarning("Validation Error", "Check-out date is required")
            return False

        # Validate check-out date format
        try:
            checkout_date = datetime.datetime.strptime(self.booking_checkout_var.get(), "%Y-%m-%d")
            checkin_date = datetime.datetime.strptime(self.booking_checkin_var.get(), "%Y-%m-%d")

            if checkout_date <= checkin_date:
                messagebox.showwarning("Validation Error", "Check-out date must be after check-in date")
                return False

        except ValueError:
            messagebox.showwarning("Validation Error", "Check-out date must be in YYYY-MM-DD format")
            return False

        return True

    def book_specific_room(self, room_id):
        """Book a specific room from the dashboard"""
        # Get room details
        self.cur.execute("SELECT room_number FROM guest_house WHERE id = ?", (room_id,))
        room_number = self.cur.fetchone()[0]

        # Find the room in the combobox
        room_index = -1
        for i, value in enumerate(self.booking_room_combo['values']):
            if value.startswith(f"{room_number} - "):
                room_index = i
                break

        if room_index >= 0:
            # Switch to booking tab
            self.notebook.select(3)  # Index 3 is the Guest House tab

            # Select the room in the combobox
            self.booking_room_combo.current(room_index)

            # Set focus to customer selection
            self.booking_customer_combo.focus_set()

    def view_booking_details(self, room_id, booking_id):
        """View details of a booking"""
        # Get booking details
        self.cur.execute("""
            SELECT b.*, c.name as customer_name, g.room_number
            FROM guest_house_bookings b
            JOIN customers c ON b.customer_id = c.id
            JOIN guest_house g ON b.room_id = g.id
            WHERE b.id = ?
        """, (booking_id,))

        booking = self.cur.fetchone()

        if booking:
            # Create a details window
            details_window = tk.Toplevel(self)
            details_window.title(f"Booking Details - Room {booking['room_number']}")
            details_window.geometry("600x500")
            details_window.transient(self)

            # Create a notebook for tabs
            notebook = ttk.Notebook(details_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Main details tab
            details_tab = ttk.Frame(notebook)
            notebook.add(details_tab, text="Booking Details")

            # Add booking details
            ttk.Label(details_tab, text="Booking Details", font=("Arial", 16, "bold")).pack(pady=10)

            details_frame = ttk.Frame(details_tab)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # Customer info
            ttk.Label(details_frame, text="Customer:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['customer_name']).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

            # Room info
            ttk.Label(details_frame, text="Room:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['room_number']).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

            # Check-in/out dates
            ttk.Label(details_frame, text="Check-in:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=f"{booking['check_in_date']} {booking['check_in_time']}").grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

            ttk.Label(details_frame, text="Check-out:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['check_out_date']).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

            # ID Card
            ttk.Label(details_frame, text="ID Card:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['id_card'] if booking['id_card'] else "N/A").grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)

            # Meal Plan
            ttk.Label(details_frame, text="Meal Plan:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['meal_plan'] if booking['meal_plan'] else "None").grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)

            # Total Amount
            ttk.Label(details_frame, text="Total Amount:", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=f"₦{booking['total_amount']:.2f}").grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)

            # Status
            ttk.Label(details_frame, text="Status:", font=("Arial", 10, "bold")).grid(row=7, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['status']).grid(row=7, column=1, sticky=tk.W, padx=5, pady=5)

            # Companions tab (if has companions)
            if booking['has_companions']:
                companions_tab = ttk.Frame(notebook)
                notebook.add(companions_tab, text="Companions")

                # Get companions
                companions = db.get_booking_companions(booking_id)

                if companions:
                    ttk.Label(companions_tab, text="Guest Companions", font=("Arial", 16, "bold")).pack(pady=10)

                    # Create a treeview for companions
                    tree_frame = ttk.Frame(companions_tab)
                    tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                    companions_tree = ttk.Treeview(tree_frame, columns=("Name", "Relationship", "ID Card", "Phone"), show="headings")

                    # Define headings
                    companions_tree.heading("Name", text="Name")
                    companions_tree.heading("Relationship", text="Relationship")
                    companions_tree.heading("ID Card", text="ID Card")
                    companions_tree.heading("Phone", text="Phone")

                    # Define columns
                    companions_tree.column("Name", width=150)
                    companions_tree.column("Relationship", width=100)
                    companions_tree.column("ID Card", width=120)
                    companions_tree.column("Phone", width=120)

                    # Add scrollbar
                    scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=companions_tree.yview)
                    companions_tree.configure(yscroll=scrollbar.set)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                    companions_tree.pack(fill=tk.BOTH, expand=True)

                    # Add companions to tree
                    for companion in companions:
                        companions_tree.insert("", tk.END, values=(
                            companion['name'],
                            companion['relationship'] if companion['relationship'] else "N/A",
                            companion['id_card'] if companion['id_card'] else "N/A",
                            companion['phone'] if companion['phone'] else "N/A"
                        ))
                else:
                    ttk.Label(companions_tab, text="No companions found", font=("Arial", 12)).pack(pady=50)

            # Orders tab
            orders_tab = ttk.Frame(notebook)
            notebook.add(orders_tab, text="Orders")

            # Get orders for this booking
            self.cur.execute("""
                SELECT * FROM guest_house_orders WHERE booking_id = ? ORDER BY order_date DESC
            """, (booking_id,))

            orders = self.cur.fetchall()

            if orders:
                ttk.Label(orders_tab, text="Room Orders", font=("Arial", 16, "bold")).pack(pady=10)

                # Create a treeview for orders
                tree_frame = ttk.Frame(orders_tab)
                tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                orders_tree = ttk.Treeview(tree_frame, columns=("Date", "Type", "Items", "Amount"), show="headings")

                # Define headings
                orders_tree.heading("Date", text="Date")
                orders_tree.heading("Type", text="Type")
                orders_tree.heading("Items", text="Items")
                orders_tree.heading("Amount", text="Amount")

                # Define columns
                orders_tree.column("Date", width=150)
                orders_tree.column("Type", width=100)
                orders_tree.column("Items", width=200)
                orders_tree.column("Amount", width=100)

                # Add scrollbar
                scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=orders_tree.yview)
                orders_tree.configure(yscroll=scrollbar.set)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                orders_tree.pack(fill=tk.BOTH, expand=True)

                # Add orders to tree
                for order in orders:
                    orders_tree.insert("", tk.END, values=(
                        order['order_date'],
                        order['order_type'],
                        order['items'],
                        f"₦{order['total_amount']:.2f}"
                    ))
            else:
                ttk.Label(orders_tab, text="No orders found", font=("Arial", 12)).pack(pady=50)

            # Close button
            ttk.Button(details_window, text="Close", command=details_window.destroy).pack(pady=10)

    def add_room_order(self, booking_id):
        """Add an order for a room"""
        # Create an order window
        order_window = tk.Toplevel(self)
        order_window.title("Add Room Order")
        order_window.geometry("400x300")
        order_window.transient(self)

        # Add order form
        ttk.Label(order_window, text="Add Room Order", font=("Arial", 16, "bold")).pack(pady=10)

        form_frame = ttk.Frame(order_window)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Order type
        ttk.Label(form_frame, text="Order Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        order_type_var = tk.StringVar()
        order_type_combo = ttk.Combobox(form_frame, textvariable=order_type_var, width=20)
        order_type_combo['values'] = ('Room Service', 'Restaurant', 'Bar', 'Laundry', 'Other')
        order_type_combo.current(0)
        order_type_combo.grid(row=0, column=1, padx=5, pady=5)

        # Items
        ttk.Label(form_frame, text="Items:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        items_var = tk.StringVar()
        items_entry = ttk.Entry(form_frame, textvariable=items_var, width=30)
        items_entry.grid(row=1, column=1, padx=5, pady=5)

        # Amount
        ttk.Label(form_frame, text="Amount (₦):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        amount_var = tk.StringVar()
        amount_entry = ttk.Entry(form_frame, textvariable=amount_var, width=15)
        amount_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        # Add order button
        def add_order():
            try:
                order_type = order_type_var.get()
                items = items_var.get()
                amount = float(amount_var.get())

                if not items:
                    messagebox.showwarning("Validation Error", "Please enter items")
                    return

                if amount <= 0:
                    messagebox.showwarning("Validation Error", "Amount must be greater than zero")
                    return

                # Add order to database
                db.add_guest_house_order(booking_id, order_type, items, amount)

                # Refresh the data
                self.refresh_guest_house()

                # Close the window
                order_window.destroy()

                # Show success message
                self.status_text.set("Order added successfully")
                messagebox.showinfo("Success", "Order added successfully")

            except ValueError:
                messagebox.showwarning("Validation Error", "Amount must be a number")
            except Exception as e:
                messagebox.showerror("Error", f"Error adding order: {str(e)}")

        ttk.Button(form_frame, text="Add Order", command=add_order).grid(row=3, column=0, columnspan=2, pady=10)

        # Cancel button
        ttk.Button(form_frame, text="Cancel", command=order_window.destroy).grid(row=4, column=0, columnspan=2, pady=5)

    def refresh_room_dashboard(self):
        """Refresh the room dashboard"""
        # Clear existing room cards
        for widget in self.room_cards_frame.winfo_children():
            widget.destroy()

        # Get all rooms with their status
        rooms = db.get_guest_house_dashboard()

        # Create a grid layout for room cards
        row = 0
        col = 0

        for room in rooms:
            # Create a frame for each room
            room_frame = ttk.LabelFrame(self.room_cards_frame, text=f"Room {room[1]}: {room[2]}")
            room_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            # Room details
            ttk.Label(room_frame, text=f"Type: {room[2]}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Capacity: {room[3]} person(s)").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Rate: ₦{room[4]:.2f} per night").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

            # Status with color coding
            status_frame = ttk.Frame(room_frame)
            status_frame.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)

            status_color = "#4CAF50" if room[5] == "Available" else "#F44336"  # Green for available, red for occupied
            status_label = ttk.Label(status_frame, text=f"Status: {room[5]}", foreground=status_color, font=("Arial", 10, "bold"))
            status_label.pack(side=tk.LEFT)

            # If room is occupied, show occupant info
            if room[5] == "Occupied" and room[10]:  # room[10] is customer_id
                occupant_frame = ttk.LabelFrame(room_frame, text="Occupant Information")
                occupant_frame.grid(row=4, column=0, sticky="ew", padx=5, pady=5)

                ttk.Label(occupant_frame, text=f"Name: {room[11]}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(occupant_frame, text=f"Check-in: {room[7]}").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(occupant_frame, text=f"Check-out: {room[9]}").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

                # Add action buttons
                button_frame = ttk.Frame(room_frame)
                button_frame.grid(row=5, column=0, pady=5)

                ttk.Button(button_frame, text="View Details",
                          command=lambda r=room[0], b=room[6]: self.view_booking_details(r, b)).pack(side=tk.LEFT, padx=2)
                ttk.Button(button_frame, text="Add Order",
                          command=lambda b=room[6]: self.add_room_order(b)).pack(side=tk.LEFT, padx=2)
            else:
                # Add book button for available rooms
                ttk.Button(room_frame, text="Book Now",
                          command=lambda r=room[0]: self.book_specific_room(r)).grid(row=5, column=0, pady=5)

            # Update grid position
            col += 1
            if col > 2:  # 3 rooms per row
                col = 0
                row += 1

        # Make rows and columns expandable
        for i in range(3):
            self.room_cards_frame.columnconfigure(i, weight=1)
        for i in range(row + 1):
            self.room_cards_frame.rowconfigure(i, weight=1)

    def load_customer_data(self):
        """Load customer data for the booking form"""
        # Get all customers
        self.cur.execute("SELECT id, name FROM customers ORDER BY name")
        customers = self.cur.fetchall()

        # Format for combobox: "ID - Name"
        customer_list = [f"{c[0]} - {c[1]}" for c in customers]
        self.booking_customer_combo['values'] = customer_list

    def load_room_data(self):
        """Load room data for the booking form"""
        # Get all rooms
        self.cur.execute("SELECT id, room_number, room_type, status FROM guest_house ORDER BY room_number")
        rooms = self.cur.fetchall()

        # Format for combobox: "Room# - Type (Status)"
        room_list = [f"{r[1]} - {r[2]} ({r[3]})" for r in rooms]
        self.booking_room_combo['values'] = room_list

    # ===== CUSTOMERS TAB =====
    def setup_customers_tab(self):
        """Setup the customers management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.customers_frame, text="Customer Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.customers_frame, text="Customer List")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Name:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Rank:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.customer_rank_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_rank_var, width=15).grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Department:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_dept_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_dept_var, width=30).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Phone:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.customer_phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_phone_var, width=15).grid(row=1, column=3, padx=5, pady=5)

        # Row 2
        ttk.Label(form_frame, text="Email:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_email_var, width=30).grid(row=2, column=1, padx=5, pady=5)

        # Row 3
        ttk.Label(form_frame, text="Address:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_address_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_address_var, width=50).grid(row=3, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W+tk.E)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=4, column=0, columnspan=4, pady=10)

        ttk.Button(button_frame, text="Add Customer", command=self.add_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Update", command=self.update_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete", command=self.delete_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_customer_form).pack(side=tk.LEFT, padx=5)

        # Search frame
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        self.customer_search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.customer_search_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Search", command=self.search_customers).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Show All", command=self.refresh_customers).pack(side=tk.LEFT, padx=5)

        # Customer list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.customer_tree = ttk.Treeview(tree_frame, columns=("ID", "Name", "Rank", "Department", "Phone", "Email", "Address", "Registration"), show="headings")

        # Define headings
        self.customer_tree.heading("ID", text="ID")
        self.customer_tree.heading("Name", text="Name")
        self.customer_tree.heading("Rank", text="Rank")
        self.customer_tree.heading("Department", text="Department")
        self.customer_tree.heading("Phone", text="Phone")
        self.customer_tree.heading("Email", text="Email")
        self.customer_tree.heading("Address", text="Address")
        self.customer_tree.heading("Registration", text="Registration Date")

        # Define columns
        self.customer_tree.column("ID", width=50)
        self.customer_tree.column("Name", width=150)
        self.customer_tree.column("Rank", width=80)
        self.customer_tree.column("Department", width=120)
        self.customer_tree.column("Phone", width=100)
        self.customer_tree.column("Email", width=150)
        self.customer_tree.column("Address", width=200)
        self.customer_tree.column("Registration", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.customer_tree.yview)
        self.customer_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.customer_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.customer_tree.bind("<<TreeviewSelect>>", self.on_customer_select)

        # Initial data load
        self.refresh_customers()

    def refresh_customers(self):
        """Refresh the customer list"""
        # Clear the treeview
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # Get all customers
        self.cur.execute("SELECT * FROM customers")
        customers = self.cur.fetchall()

        # Add to treeview
        for customer in customers:
            # customer format: id, name, rank, department, phone, email, registration_date
            self.customer_tree.insert("", tk.END, values=customer)

    def on_customer_select(self, event):
        """Handle customer selection"""
        selected_item = self.customer_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.customer_tree.item(selected_item[0])
        values = item['values']

        # Fill the form fields
        self.customer_name_var.set(values[1])
        self.customer_rank_var.set(values[2] if values[2] else "")
        self.customer_dept_var.set(values[3] if values[3] else "")
        self.customer_phone_var.set(values[4] if values[4] else "")
        self.customer_email_var.set(values[5] if values[5] else "")
        self.customer_address_var.set(values[6] if values[6] else "")

        # Store the selected customer ID
        self.cur_selected_customer_id = values[0]

    def add_customer(self):
        """Add a new customer"""
        # Validate inputs
        if not self.validate_customer_form():
            return

        try:
            # Get form values
            name = self.customer_name_var.get()
            rank = self.customer_rank_var.get()
            department = self.customer_dept_var.get()
            phone = self.customer_phone_var.get()
            email = self.customer_email_var.get()
            address = self.customer_address_var.get()

            # Add to database
            customer_id = db.add_customer(name, rank, department, phone, email, address)

            # Refresh the list
            self.refresh_customers()

            # Clear the form
            self.clear_customer_form()

            # Show success message
            self.status_text.set(f"Customer '{name}' added")
            messagebox.showinfo("Success", f"Customer '{name}' added")

        except Exception as e:
            messagebox.showerror("Error", f"Error adding customer: {str(e)}")

    def update_customer(self):
        """Update an existing customer"""
        if not hasattr(self, 'cur_selected_customer_id') or not self.cur_selected_customer_id:
            messagebox.showwarning("Selection Required", "Please select a customer to update")
            return

        # Validate inputs
        if not self.validate_customer_form():
            return

        try:
            # Get form values
            name = self.customer_name_var.get()
            rank = self.customer_rank_var.get()
            department = self.customer_dept_var.get()
            phone = self.customer_phone_var.get()
            email = self.customer_email_var.get()
            address = self.customer_address_var.get()

            # Update in database
            self.cur.execute("""
                UPDATE customers
                SET name = ?, rank = ?, department = ?, phone = ?, email = ?, address = ?
                WHERE id = ?
            """, (name, rank, department, phone, email, address, self.cur_selected_customer_id))
            self.conn.commit()

            # Refresh the list
            self.refresh_customers()

            # Show success message
            self.status_text.set(f"Customer '{name}' updated")
            messagebox.showinfo("Success", f"Customer '{name}' updated")

        except Exception as e:
            messagebox.showerror("Error", f"Error updating customer: {str(e)}")

    def delete_customer(self):
        """Delete a customer"""
        if not hasattr(self, 'cur_selected_customer_id') or not self.cur_selected_customer_id:
            messagebox.showwarning("Selection Required", "Please select a customer to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this customer?"):
            return

        try:
            # Check if customer has related records
            self.cur.execute("""
                SELECT COUNT(*) FROM (
                    SELECT customer_id FROM restaurant_orders WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM gym_memberships WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM guest_house_bookings WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM invoices WHERE customer_id = ?
                )
            """, (self.cur_selected_customer_id, self.cur_selected_customer_id,
                  self.cur_selected_customer_id, self.cur_selected_customer_id))

            related_count = self.cur.fetchone()[0]

            if related_count > 0:
                if not messagebox.askyesno("Warning",
                                          "This customer has related records in other modules. "
                                          "Deleting will remove all related data. Continue?"):
                    return

            # Delete from database
            self.cur.execute("DELETE FROM customers WHERE id = ?", (self.cur_selected_customer_id,))
            self.conn.commit()

            # Refresh the list
            self.refresh_customers()

            # Clear the form
            self.clear_customer_form()

            # Show success message
            self.status_text.set("Customer deleted")

        except Exception as e:
            messagebox.showerror("Error", f"Error deleting customer: {str(e)}")

    def clear_customer_form(self):
        """Clear the customer form fields"""
        self.customer_name_var.set("")
        self.customer_rank_var.set("")
        self.customer_dept_var.set("")
        self.customer_phone_var.set("")
        self.customer_email_var.set("")
        self.customer_address_var.set("")
        if hasattr(self, 'cur_selected_customer_id'):
            delattr(self, 'cur_selected_customer_id')

    def validate_customer_form(self):
        """Validate customer form inputs"""
        if not self.customer_name_var.get():
            messagebox.showwarning("Validation Error", "Customer name is required")
            return False

        # Validate email format if provided
        email = self.customer_email_var.get()
        if email and '@' not in email:
            messagebox.showwarning("Validation Error", "Invalid email format")
            return False

        return True

    def search_customers(self):
        """Search customers"""
        search_term = self.customer_search_var.get().strip().lower()
        if not search_term:
            self.refresh_customers()
            return

        # Clear the treeview
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # Search in database
        customers = db.search_customers(search_term)

        # Add to treeview
        for customer in customers:
            self.customer_tree.insert("", tk.END, values=customer)

    # ===== INVOICES TAB =====
    def setup_invoices_tab(self):
        """Setup the invoices management tab"""
        # Placeholder for now
        ttk.Label(self.invoices_frame, text="Invoice Management - To be implemented").pack(pady=20)

    def refresh_invoices(self):
        """Refresh invoices data"""
        # Placeholder for now
        pass


# Main entry point
if __name__ == "__main__":
    try:
        # Try to import required packages
        import qrcode
        from PIL import Image, ImageTk
    except ImportError:
        import tkinter.messagebox as mb
        mb.showerror("Missing Dependencies",
                    "This application requires additional packages.\n"
                    "Please install them using pip:\n\n"
                    "pip install qrcode pillow")
        exit(1)

    app = CommandFacilitiesApp()
    app.mainloop()
