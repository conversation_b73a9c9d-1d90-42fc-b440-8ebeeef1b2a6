import tkinter as tk
from tkinter import ttk, messagebox, Menu, filedialog
import os
import csv
import datetime
import qrcode
from PIL import Image, ImageTk
from io import BytesIO
import base64
from enhanced_db import Database

# Initialize database
db = Database('hq_logistics.db')

class HQLogisticsApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title('HQLogistics Command Welfare Shop')
        self.geometry("1200x800")
        self.configure(bg="#f0f0f0")

        # Set application icon
        # self.iconbitmap('icon.ico')  # Uncomment and add icon file if available

        # Initialize database connection
        self.db = db
        self.conn = self.db.conn
        self.cur = self.db.cur

        # Create main menu
        self.create_menu()

        # Create status bar
        self.status_text = tk.StringVar()
        self.status_text.set('Welcome to HQLogistics Command Welfare Shop')
        self.status_bar = tk.Label(self, textvariable=self.status_text, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs for different modules
        self.inventory_frame = ttk.Frame(self.notebook)
        self.restaurant_frame = ttk.Frame(self.notebook)
        self.gym_frame = ttk.Frame(self.notebook)
        self.guest_house_frame = ttk.Frame(self.notebook)
        self.customers_frame = ttk.Frame(self.notebook)
        self.invoices_frame = ttk.Frame(self.notebook)

        # Add tabs to notebook
        self.notebook.add(self.inventory_frame, text="Inventory")
        self.notebook.add(self.restaurant_frame, text="Restaurants")
        self.notebook.add(self.gym_frame, text="Gym")
        self.notebook.add(self.guest_house_frame, text="Guest House")
        self.notebook.add(self.customers_frame, text="Customers")
        self.notebook.add(self.invoices_frame, text="Invoices")

        # Initialize all tabs
        self.setup_inventory_tab()
        self.setup_restaurant_tab()
        self.setup_gym_tab()
        self.setup_guest_house_tab()
        self.setup_customers_tab()
        self.setup_invoices_tab()

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def create_menu(self):
        """Create the main menu bar"""
        self.menu_bar = Menu(self)
        self.config(menu=self.menu_bar)

        # File menu
        file_menu = Menu(self.menu_bar, tearoff=0)
        file_menu.add_command(label="Export Data", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.destroy)
        self.menu_bar.add_cascade(label="File", menu=file_menu)

        # Edit menu
        edit_menu = Menu(self.menu_bar, tearoff=0)
        edit_menu.add_command(label="Preferences", command=self.show_preferences)
        self.menu_bar.add_cascade(label="Edit", menu=edit_menu)

        # Reports menu
        reports_menu = Menu(self.menu_bar, tearoff=0)
        reports_menu.add_command(label="Inventory Report", command=lambda: self.generate_report("inventory"))
        reports_menu.add_command(label="Sales Report", command=lambda: self.generate_report("sales"))
        reports_menu.add_command(label="Gym Usage Report", command=lambda: self.generate_report("gym"))
        reports_menu.add_command(label="Guest House Occupancy", command=lambda: self.generate_report("guest_house"))
        self.menu_bar.add_cascade(label="Reports", menu=reports_menu)

        # Help menu
        help_menu = Menu(self.menu_bar, tearoff=0)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
        help_menu.add_command(label="About", command=self.show_about)
        self.menu_bar.add_cascade(label="Help", menu=help_menu)

    def on_tab_changed(self, event):
        """Handle tab change events"""
        tab_id = self.notebook.select()
        tab_name = self.notebook.tab(tab_id, "text")
        self.status_text.set(f"Current module: {tab_name}")

        # Refresh data in the selected tab
        if tab_name == "Inventory":
            self.refresh_inventory()
        elif tab_name == "Restaurants":
            self.refresh_restaurants()
        elif tab_name == "Gym":
            self.refresh_gym()
        elif tab_name == "Guest House":
            self.refresh_guest_house()
        elif tab_name == "Customers":
            self.refresh_customers()
        elif tab_name == "Invoices":
            self.refresh_invoices()

    def export_data(self):
        """Export data to CSV based on current tab"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")

        filename = filedialog.asksaveasfilename(
            initialdir="/",
            title=f"Export {current_tab} Data",
            filetypes=(("CSV files", "*.csv"), ("All files", "*.*"))
        )

        if not filename:
            return

        if not filename.endswith('.csv'):
            filename += '.csv'

        try:
            with open(filename, 'w', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)

                if current_tab == "Inventory":
                    # Export inventory data
                    csv_writer.writerow(['ID', 'Item Name', 'Category', 'Supplier', 'Price',
                                        'Quantity', 'Expiration Date', 'Purchase Date'])
                    for item in db.fetch_inventory():
                        # Skip QR code and notes in export
                        csv_writer.writerow([item[0], item[1], item[2], item[3], item[4],
                                            item[5], item[6], item[7]])

                # Add export logic for other tabs as needed

            self.status_text.set(f'{current_tab} data exported to {os.path.basename(filename)}')
            messagebox.showinfo("Export Successful", f"Data exported to {filename}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting data: {str(e)}")

    def show_preferences(self):
        """Show preferences dialog"""
        preferences_window = tk.Toplevel(self)
        preferences_window.title("Preferences")
        preferences_window.geometry("400x300")
        preferences_window.transient(self)
        preferences_window.grab_set()

        # Add preferences options here
        ttk.Label(preferences_window, text="Application Preferences", font=("Arial", 14, "bold")).pack(pady=10)

        # Example preference options
        ttk.Checkbutton(preferences_window, text="Enable notifications").pack(anchor=tk.W, padx=20, pady=5)
        ttk.Checkbutton(preferences_window, text="Dark mode").pack(anchor=tk.W, padx=20, pady=5)

        ttk.Button(preferences_window, text="Save", command=preferences_window.destroy).pack(pady=20)

    def generate_report(self, report_type):
        """Generate and display reports"""
        report_window = tk.Toplevel(self)
        report_window.title(f"{report_type.title()} Report")
        report_window.geometry("800x600")
        report_window.transient(self)

        # Add report generation logic based on type
        ttk.Label(report_window, text=f"{report_type.title()} Report", font=("Arial", 16, "bold")).pack(pady=10)

        # Example report content
        report_frame = ttk.Frame(report_window)
        report_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Add report-specific content here

        # Export button
        ttk.Button(report_window, text="Export Report",
                  command=lambda: self.export_report(report_type)).pack(pady=10)

    def export_report(self, report_type):
        """Export a report to CSV or PDF"""
        # Implementation would depend on report type
        messagebox.showinfo("Export Report", f"{report_type.title()} report export functionality to be implemented")

    def show_user_guide(self):
        """Show user guide"""
        guide_window = tk.Toplevel(self)
        guide_window.title("User Guide")
        guide_window.geometry("800x600")
        guide_window.transient(self)

        # Add user guide content
        ttk.Label(guide_window, text="HQLogistics Command Welfare Shop - User Guide",
                 font=("Arial", 16, "bold")).pack(pady=10)

        # Create a scrollable text widget for the guide
        guide_frame = ttk.Frame(guide_window)
        guide_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        scrollbar = ttk.Scrollbar(guide_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        guide_text = tk.Text(guide_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set)
        guide_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=guide_text.yview)

        # Add guide content
        guide_content = """
        # HQLogistics Command Welfare Shop User Guide

        ## Overview
        This application manages various facilities at the command including inventory,
        restaurants, gym, and guest house.

        ## Modules

        ### Inventory
        - Add, update, and remove items from inventory
        - Track quantities and expiration dates
        - Generate QR codes for items

        ### Restaurants
        - Manage menu items for Hub Mess and Ola Saad restaurants
        - Process food orders
        - Generate invoices for purchases

        ### Gym
        - Track memberships
        - Record check-in and check-out times
        - Monitor usage statistics

        ### Guest House
        - Manage room bookings
        - Track occupancy
        - Process payments

        ### Customers
        - Maintain customer database
        - Track purchase history

        ### Invoices
        - Generate invoices for all transactions
        - Track payment status
        """

        guide_text.insert(tk.END, guide_content)
        guide_text.config(state=tk.DISABLED)  # Make read-only

    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo(
            "About HQLogistics",
            "HQLogistics Command Welfare Shop\nVersion 1.0\n\n"
            "A comprehensive management system for military base facilities including "
            "inventory, restaurants, gym, and guest house."
        )

    # ===== INVENTORY TAB =====
    def setup_inventory_tab(self):
        """Setup the inventory management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.inventory_frame, text="Item Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.inventory_frame, text="Inventory Items")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        qr_frame = ttk.LabelFrame(self.inventory_frame, text="QR Code")
        qr_frame.pack(fill=tk.Y, side=tk.RIGHT, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Item Name:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.item_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.item_name_var, width=20).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Category:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(form_frame, textvariable=self.category_var, width=15)
        category_combo['values'] = ('Food', 'Beverages', 'Electronics', 'Clothing', 'Household', 'General')
        category_combo.grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Supplier:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.supplier_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.supplier_var, width=20).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Price:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.price_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.price_var, width=15).grid(row=1, column=3, padx=5, pady=5)

        # Row 2
        ttk.Label(form_frame, text="Quantity:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.quantity_var = tk.StringVar()
        self.quantity_var.set("1")
        ttk.Entry(form_frame, textvariable=self.quantity_var, width=10).grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(form_frame, text="Expiration Date:").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        self.expiry_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.expiry_var, width=15).grid(row=2, column=3, padx=5, pady=5)
        ttk.Label(form_frame, text="(YYYY-MM-DD)").grid(row=2, column=4, padx=0, pady=5, sticky=tk.W)

        # Row 3
        ttk.Label(form_frame, text="Notes:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.notes_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.notes_var, width=40).grid(row=3, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W+tk.E)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=4, column=0, columnspan=5, pady=10)

        ttk.Button(button_frame, text="Add Item", command=self.add_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Update", command=self.update_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete", command=self.delete_inventory_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_inventory_form).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Generate QR", command=self.generate_item_qr).pack(side=tk.LEFT, padx=5)

        # Search frame
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        self.inventory_search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.inventory_search_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Search", command=self.search_inventory).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Show All", command=self.refresh_inventory).pack(side=tk.LEFT, padx=5)

        # Filter by category
        ttk.Label(search_frame, text="Filter by Category:").pack(side=tk.LEFT, padx=5)
        self.filter_category_var = tk.StringVar()
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_category_var, width=15)
        filter_combo['values'] = ('All', 'Food', 'Beverages', 'Electronics', 'Clothing', 'Household', 'General')
        filter_combo.current(0)
        filter_combo.pack(side=tk.LEFT, padx=5)
        filter_combo.bind("<<ComboboxSelected>>", lambda e: self.filter_inventory_by_category())

        # Inventory list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.inventory_tree = ttk.Treeview(tree_frame, columns=("ID", "Name", "Category", "Supplier", "Price", "Quantity", "Expiry"), show="headings")

        # Define headings
        self.inventory_tree.heading("ID", text="ID")
        self.inventory_tree.heading("Name", text="Item Name")
        self.inventory_tree.heading("Category", text="Category")
        self.inventory_tree.heading("Supplier", text="Supplier")
        self.inventory_tree.heading("Price", text="Price")
        self.inventory_tree.heading("Quantity", text="Qty")
        self.inventory_tree.heading("Expiry", text="Expiration Date")

        # Define columns
        self.inventory_tree.column("ID", width=50)
        self.inventory_tree.column("Name", width=150)
        self.inventory_tree.column("Category", width=100)
        self.inventory_tree.column("Supplier", width=120)
        self.inventory_tree.column("Price", width=80)
        self.inventory_tree.column("Quantity", width=60)
        self.inventory_tree.column("Expiry", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.inventory_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.inventory_tree.bind("<<TreeviewSelect>>", self.on_inventory_select)

        # QR code display
        self.qr_label = ttk.Label(qr_frame, text="Select an item to display QR code")
        self.qr_label.pack(padx=10, pady=10)

        # Initial data load
        self.refresh_inventory()

    def refresh_inventory(self):
        """Refresh the inventory list"""
        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Get all inventory items
        items = db.fetch_inventory()

        # Add to treeview
        for item in items:
            # item format: id, name, category, supplier, price, quantity, expiry_date, purchase_date, qr_code, notes
            self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def on_inventory_select(self, event):
        """Handle inventory item selection"""
        selected_item = self.inventory_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.inventory_tree.item(selected_item[0])
        values = item['values']

        # Fill the form fields
        self.item_name_var.set(values[1])
        self.category_var.set(values[2])
        self.supplier_var.set(values[3])
        self.price_var.set(values[4])
        self.quantity_var.set(values[5])
        self.expiry_var.set(values[6] if values[6] else "")

        # Get full item details including notes and QR code
        self.cur_selected_id = values[0]

        # Display QR code if available
        # This would need to be implemented to fetch the QR code from the database
        self.display_qr_code(self.cur_selected_id)

    def add_inventory_item(self):
        """Add a new inventory item"""
        # Validate inputs
        if not self.validate_inventory_form():
            return

        try:
            # Get form values
            name = self.item_name_var.get()
            category = self.category_var.get()
            supplier = self.supplier_var.get()
            price = float(self.price_var.get())
            quantity = int(self.quantity_var.get())
            expiry = self.expiry_var.get() if self.expiry_var.get() else None
            notes = self.notes_var.get()

            # Add to database
            db.insert_inventory(name, category, supplier, price, quantity, expiry, notes)

            # Refresh the list
            self.refresh_inventory()

            # Clear the form
            self.clear_inventory_form()

            # Show success message
            self.status_text.set(f"Item '{name}' added to inventory")
            messagebox.showinfo("Success", f"Item '{name}' added to inventory")

        except Exception as e:
            messagebox.showerror("Error", f"Error adding item: {str(e)}")

    def update_inventory_item(self):
        """Update an existing inventory item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to update")
            return

        # Validate inputs
        if not self.validate_inventory_form():
            return

        try:
            # Get form values
            name = self.item_name_var.get()
            category = self.category_var.get()
            supplier = self.supplier_var.get()
            price = float(self.price_var.get())
            quantity = int(self.quantity_var.get())
            expiry = self.expiry_var.get() if self.expiry_var.get() else None
            notes = self.notes_var.get()

            # Update in database
            db.update_inventory(self.cur_selected_id, name, category, supplier, price, quantity, expiry, notes)

            # Refresh the list
            self.refresh_inventory()

            # Show success message
            self.status_text.set(f"Item '{name}' updated")
            messagebox.showinfo("Success", f"Item '{name}' updated")

        except Exception as e:
            messagebox.showerror("Error", f"Error updating item: {str(e)}")

    def delete_inventory_item(self):
        """Delete an inventory item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this item?"):
            return

        try:
            # Delete from database
            db.remove_inventory(self.cur_selected_id)

            # Refresh the list
            self.refresh_inventory()

            # Clear the form
            self.clear_inventory_form()

            # Show success message
            self.status_text.set("Item deleted from inventory")

        except Exception as e:
            messagebox.showerror("Error", f"Error deleting item: {str(e)}")

    def clear_inventory_form(self):
        """Clear the inventory form fields"""
        self.item_name_var.set("")
        self.category_var.set("")
        self.supplier_var.set("")
        self.price_var.set("")
        self.quantity_var.set("1")
        self.expiry_var.set("")
        self.notes_var.set("")
        if hasattr(self, 'cur_selected_id'):
            delattr(self, 'cur_selected_id')

        # Clear QR code display
        self.qr_label.config(text="Select an item to display QR code", image="")

    def validate_inventory_form(self):
        """Validate inventory form inputs"""
        if not self.item_name_var.get():
            messagebox.showwarning("Validation Error", "Item name is required")
            return False

        if not self.price_var.get():
            messagebox.showwarning("Validation Error", "Price is required")
            return False

        try:
            price = float(self.price_var.get())
            if price < 0:
                messagebox.showwarning("Validation Error", "Price must be a positive number")
                return False
        except ValueError:
            messagebox.showwarning("Validation Error", "Price must be a number")
            return False

        try:
            quantity = int(self.quantity_var.get())
            if quantity < 0:
                messagebox.showwarning("Validation Error", "Quantity must be a positive integer")
                return False
        except ValueError:
            messagebox.showwarning("Validation Error", "Quantity must be an integer")
            return False

        # Validate expiration date format if provided
        if self.expiry_var.get():
            try:
                datetime.datetime.strptime(self.expiry_var.get(), "%Y-%m-%d")
            except ValueError:
                messagebox.showwarning("Validation Error", "Expiration date must be in YYYY-MM-DD format")
                return False

        return True

    def search_inventory(self):
        """Search inventory items"""
        search_term = self.inventory_search_var.get().strip().lower()
        if not search_term:
            self.refresh_inventory()
            return

        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Get all inventory items
        items = db.fetch_inventory()

        # Filter and add to treeview
        for item in items:
            # Check if search term is in any of the relevant fields
            if (search_term in str(item[1]).lower() or  # name
                search_term in str(item[2]).lower() or  # category
                search_term in str(item[3]).lower()):   # supplier

                self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def filter_inventory_by_category(self):
        """Filter inventory by selected category"""
        category = self.filter_category_var.get()

        # Clear the treeview
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        if category == "All":
            self.refresh_inventory()
            return

        # Get filtered inventory items
        items = db.fetch_inventory(category)

        # Add to treeview
        for item in items:
            self.inventory_tree.insert("", tk.END, values=(item[0], item[1], item[2], item[3], item[4], item[5], item[6]))

    def generate_item_qr(self):
        """Generate QR code for the current item"""
        if not hasattr(self, 'cur_selected_id') or not self.cur_selected_id:
            messagebox.showwarning("Selection Required", "Please select an item to generate QR code")
            return

        # This would need to be implemented to generate and save QR code to the database
        # For now, just display a sample QR code
        self.display_qr_code(self.cur_selected_id)

        self.status_text.set("QR code generated")

    def display_qr_code(self, item_id):
        """Display QR code for an item"""
        # This is a placeholder. In a real implementation, you would fetch the QR code from the database
        # For demonstration, we'll generate a simple QR code
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(f"ITEM:{item_id}")
            qr.make(fit=True)
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to PhotoImage
            img = img.resize((200, 200))
            photo_img = ImageTk.PhotoImage(img)

            # Update label
            self.qr_label.config(image=photo_img, text="")
            self.qr_label.image = photo_img  # Keep a reference

        except Exception as e:
            self.qr_label.config(text=f"Error displaying QR code: {str(e)}", image="")

    # ===== RESTAURANT TAB =====
    def setup_restaurant_tab(self):
        """Setup the restaurant management tab"""
        # Create notebook for restaurant sub-tabs
        restaurant_notebook = ttk.Notebook(self.restaurant_frame)
        restaurant_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create sub-tabs
        self.menu_frame = ttk.Frame(restaurant_notebook)
        self.orders_frame = ttk.Frame(restaurant_notebook)

        restaurant_notebook.add(self.menu_frame, text="Menu Management")
        restaurant_notebook.add(self.orders_frame, text="Orders")

        # Setup each sub-tab
        self.setup_menu_tab()
        self.setup_orders_tab()

    def setup_menu_tab(self):
        """Setup the menu management tab"""
        # Placeholder for now
        ttk.Label(self.menu_frame, text="Restaurant Menu Management - To be implemented").pack(pady=20)

    def setup_orders_tab(self):
        """Setup the orders management tab"""
        # Placeholder for now
        ttk.Label(self.orders_frame, text="Restaurant Orders Management - To be implemented").pack(pady=20)

    def refresh_restaurants(self):
        """Refresh restaurant data"""
        # Placeholder for now
        pass

    # ===== GYM TAB =====
    def setup_gym_tab(self):
        """Setup the gym management tab"""
        # Placeholder for now
        ttk.Label(self.gym_frame, text="Gym Membership Management - To be implemented").pack(pady=20)

    def refresh_gym(self):
        """Refresh gym data"""
        # Placeholder for now
        pass

    # ===== GUEST HOUSE TAB =====
    def setup_guest_house_tab(self):
        """Setup the guest house management tab"""
        # Initialize guest house rooms if needed
        db.initialize_guest_house_rooms()

        # Create notebook for guest house sub-tabs
        guest_house_notebook = ttk.Notebook(self.guest_house_frame)
        guest_house_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create sub-tabs
        self.room_dashboard_frame = ttk.Frame(guest_house_notebook)
        self.booking_frame = ttk.Frame(guest_house_notebook)
        self.orders_frame = ttk.Frame(guest_house_notebook)

        guest_house_notebook.add(self.room_dashboard_frame, text="Room Dashboard")
        guest_house_notebook.add(self.booking_frame, text="Bookings")
        guest_house_notebook.add(self.orders_frame, text="Orders")

        # Setup each sub-tab
        self.setup_room_dashboard()
        self.setup_booking_tab()
        self.setup_orders_tab()

    def setup_room_dashboard(self):
        """Setup the room dashboard tab"""
        # Create a frame for the room cards
        self.rooms_frame = ttk.Frame(self.room_dashboard_frame)
        self.rooms_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a title
        ttk.Label(self.rooms_frame, text="Guest House Room Status", font=("Arial", 16, "bold")).pack(pady=10)

        # Create a frame for the room cards
        self.room_cards_frame = ttk.Frame(self.rooms_frame)
        self.room_cards_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Refresh the room dashboard
        self.refresh_room_dashboard()

    def setup_booking_tab(self):
        """Setup the booking management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.booking_frame, text="Booking Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.booking_frame, text="Booking List")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Customer:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_customer_var = tk.StringVar()
        self.booking_customer_combo = ttk.Combobox(form_frame, textvariable=self.booking_customer_var, width=30)
        self.booking_customer_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Room:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_room_var = tk.StringVar()
        self.booking_room_combo = ttk.Combobox(form_frame, textvariable=self.booking_room_var, width=20)
        self.booking_room_combo.grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Check-in Date:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_checkin_var = tk.StringVar()
        self.booking_checkin_var.set(datetime.datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(form_frame, textvariable=self.booking_checkin_var, width=15, state="readonly").grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Check-out Date:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_checkout_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.booking_checkout_var, width=15).grid(row=1, column=3, padx=5, pady=5)
        ttk.Label(form_frame, text="(YYYY-MM-DD)").grid(row=1, column=4, padx=0, pady=5, sticky=tk.W)

        # Row 2
        ttk.Label(form_frame, text="ID Card:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.booking_idcard_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.booking_idcard_var, width=20).grid(row=2, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Meal Plan:").grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        self.booking_meal_var = tk.StringVar()
        meal_combo = ttk.Combobox(form_frame, textvariable=self.booking_meal_var, width=15)
        meal_combo['values'] = ('None', 'Breakfast Only', 'Half Board', 'Full Board')
        meal_combo.current(0)
        meal_combo.grid(row=2, column=3, padx=5, pady=5)

        # Row 3 - Companions
        ttk.Label(form_frame, text="Companions:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.has_companions_var = tk.BooleanVar()
        self.has_companions_var.set(False)
        ttk.Checkbutton(form_frame, text="Guest has companions", variable=self.has_companions_var,
                       command=self.toggle_companions_frame).grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)

        # Companions frame (initially hidden)
        self.companions_frame = ttk.LabelFrame(form_frame, text="Companion Details")
        self.companions_frame.grid(row=4, column=0, columnspan=5, padx=5, pady=5, sticky=tk.W+tk.E)
        self.companions_frame.grid_remove()  # Hide initially

        # Companion list
        self.companions_list = []

        # Add companion button
        ttk.Button(self.companions_frame, text="Add Companion", command=self.add_companion_dialog).pack(pady=5)

        # Companions listbox
        self.companions_listbox = tk.Listbox(self.companions_frame, width=60, height=4)
        self.companions_listbox.pack(fill=tk.X, padx=5, pady=5)

        # Remove companion button
        ttk.Button(self.companions_frame, text="Remove Selected Companion",
                  command=self.remove_companion).pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=5, column=0, columnspan=5, pady=10)

        ttk.Button(button_frame, text="Book Room", command=self.book_room).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Check Out", command=self.check_out_guest).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_booking_form).pack(side=tk.LEFT, padx=5)

        # Booking list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.booking_tree = ttk.Treeview(tree_frame, columns=("ID", "Room", "Customer", "Check-in", "Check-out", "Status", "Total"), show="headings")

        # Define headings
        self.booking_tree.heading("ID", text="ID")
        self.booking_tree.heading("Room", text="Room")
        self.booking_tree.heading("Customer", text="Customer")
        self.booking_tree.heading("Check-in", text="Check-in Date")
        self.booking_tree.heading("Check-out", text="Check-out Date")
        self.booking_tree.heading("Status", text="Status")
        self.booking_tree.heading("Total", text="Total Amount")

        # Define columns
        self.booking_tree.column("ID", width=50)
        self.booking_tree.column("Room", width=100)
        self.booking_tree.column("Customer", width=150)
        self.booking_tree.column("Check-in", width=100)
        self.booking_tree.column("Check-out", width=100)
        self.booking_tree.column("Status", width=100)
        self.booking_tree.column("Total", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.booking_tree.yview)
        self.booking_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.booking_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.booking_tree.bind("<<TreeviewSelect>>", self.on_booking_select)

        # Load customer and room data
        self.load_customer_data()
        self.load_room_data()

        # Initial data load
        self.refresh_bookings()

    def setup_orders_tab(self):
        """Setup the orders management tab"""
        # Placeholder for now
        ttk.Label(self.orders_frame, text="Guest House Orders - To be implemented").pack(pady=20)

    def refresh_guest_house(self):
        """Refresh all guest house data"""
        self.refresh_room_dashboard()
        self.refresh_bookings()

    def refresh_bookings(self):
        """Refresh the booking list"""
        # Clear the treeview
        for item in self.booking_tree.get_children():
            self.booking_tree.delete(item)

        # Get all bookings with room and customer info
        self.cur.execute("""
            SELECT b.id, g.room_number, c.name, b.check_in_date, b.check_out_date,
                   b.status, b.total_amount
            FROM guest_house_bookings b
            JOIN guest_house g ON b.room_id = g.id
            JOIN customers c ON b.customer_id = c.id
            ORDER BY b.check_in_date DESC
        """)

        bookings = self.cur.fetchall()

        # Add to treeview
        for booking in bookings:
            self.booking_tree.insert("", tk.END, values=booking)

    def on_booking_select(self, event):
        """Handle booking selection"""
        selected_item = self.booking_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.booking_tree.item(selected_item[0])
        values = item['values']

        # Store the selected booking ID
        self.cur_selected_booking_id = values[0]

        # Get room and customer IDs for the booking
        self.cur.execute("""
            SELECT room_id, customer_id, check_out_date, id_card, meal_plan
            FROM guest_house_bookings
            WHERE id = ?
        """, (self.cur_selected_booking_id,))

        booking_details = self.cur.fetchone()
        if booking_details:
            room_id, customer_id, check_out_date, id_card, meal_plan = booking_details

            # Set the form fields
            # Find the customer in the combobox
            self.cur.execute("SELECT name FROM customers WHERE id = ?", (customer_id,))
            customer_name = self.cur.fetchone()[0]
            customer_index = -1
            for i, value in enumerate(self.booking_customer_combo['values']):
                if value.startswith(f"{customer_id} - "):
                    customer_index = i
                    break
            if customer_index >= 0:
                self.booking_customer_combo.current(customer_index)

            # Find the room in the combobox
            self.cur.execute("SELECT room_number, room_type FROM guest_house WHERE id = ?", (room_id,))
            room_info = self.cur.fetchone()
            if room_info:
                room_number, room_type = room_info
                room_index = -1
                for i, value in enumerate(self.booking_room_combo['values']):
                    if value.startswith(f"{room_number} - "):
                        room_index = i
                        break
                if room_index >= 0:
                    self.booking_room_combo.current(room_index)

            # Set other fields
            self.booking_checkout_var.set(check_out_date)
            self.booking_idcard_var.set(id_card if id_card else "")

            # Set meal plan
            if meal_plan:
                meal_index = 0
                meal_options = self.booking_meal_var.get()
                for i, option in enumerate(['None', 'Breakfast Only', 'Half Board', 'Full Board']):
                    if option == meal_plan:
                        meal_index = i
                        break
                self.booking_meal_var.set(meal_plan)

    def toggle_companions_frame(self):
        """Show or hide the companions frame based on checkbox state"""
        if self.has_companions_var.get():
            self.companions_frame.grid()
        else:
            self.companions_frame.grid_remove()

    def add_companion_dialog(self):
        """Open dialog to add a companion"""
        # Create a dialog window
        dialog = tk.Toplevel(self)
        dialog.title("Add Companion")
        dialog.geometry("400x300")
        dialog.transient(self)
        dialog.grab_set()  # Make dialog modal

        # Create form fields
        form_frame = ttk.Frame(dialog, padding=10)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Name field
        ttk.Label(form_frame, text="Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        # Relationship field
        ttk.Label(form_frame, text="Relationship:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        relationship_var = tk.StringVar()
        relationship_combo = ttk.Combobox(form_frame, textvariable=relationship_var, width=20)
        relationship_combo['values'] = ('Spouse', 'Child', 'Parent', 'Sibling', 'Friend', 'Colleague', 'Other')
        relationship_combo.grid(row=1, column=1, padx=5, pady=5)

        # ID Card field
        ttk.Label(form_frame, text="ID Card:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        id_card_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=id_card_var, width=20).grid(row=2, column=1, padx=5, pady=5)

        # Phone field
        ttk.Label(form_frame, text="Phone:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=phone_var, width=20).grid(row=3, column=1, padx=5, pady=5)

        # Add button
        def add_companion():
            name = name_var.get()
            if not name:
                messagebox.showwarning("Validation Error", "Companion name is required", parent=dialog)
                return

            # Create companion dictionary
            companion = {
                'name': name,
                'relationship': relationship_var.get(),
                'id_card': id_card_var.get(),
                'phone': phone_var.get()
            }

            # Add to list and update listbox
            self.companions_list.append(companion)
            self.update_companions_listbox()

            # Close dialog
            dialog.destroy()

        ttk.Button(form_frame, text="Add Companion", command=add_companion).grid(row=4, column=0, columnspan=2, pady=10)

        # Cancel button
        ttk.Button(form_frame, text="Cancel", command=dialog.destroy).grid(row=5, column=0, columnspan=2, pady=5)

    def update_companions_listbox(self):
        """Update the companions listbox with current companions"""
        self.companions_listbox.delete(0, tk.END)
        for companion in self.companions_list:
            display_text = f"{companion['name']} - {companion['relationship'] if companion['relationship'] else 'N/A'}"
            self.companions_listbox.insert(tk.END, display_text)

    def remove_companion(self):
        """Remove the selected companion from the list"""
        selected_index = self.companions_listbox.curselection()
        if not selected_index:
            messagebox.showwarning("Selection Required", "Please select a companion to remove")
            return

        # Remove from list and update listbox
        self.companions_list.pop(selected_index[0])
        self.update_companions_listbox()

    def book_room(self):
        """Book a room"""
        # Validate inputs
        if not self.validate_booking_form():
            return

        try:
            # Parse customer ID from combobox
            customer_str = self.booking_customer_var.get()
            customer_id = int(customer_str.split(' - ')[0])

            # Parse room ID from combobox
            room_str = self.booking_room_var.get()
            room_number = room_str.split(' - ')[0]

            # Get room ID from room number
            self.cur.execute("SELECT id, status, capacity FROM guest_house WHERE room_number = ?", (room_number,))
            room_info = self.cur.fetchone()

            if not room_info:
                messagebox.showwarning("Room Not Found", "The selected room was not found.")
                return

            room_id, status, capacity = room_info

            if status != "Available":
                messagebox.showwarning("Room Not Available", "The selected room is not available for booking.")
                return

            # Check if companions exceed room capacity
            companion_count = len(self.companions_list)
            if self.has_companions_var.get() and companion_count + 1 > capacity:
                messagebox.showwarning("Capacity Exceeded",
                                      f"Room capacity ({capacity}) exceeded with {companion_count + 1} guests.")
                return

            # Get form values
            check_out_date = self.booking_checkout_var.get()
            id_card = self.booking_idcard_var.get()
            meal_plan = self.booking_meal_var.get() if self.booking_meal_var.get() != "None" else None

            # Get companions if any
            companions = self.companions_list if self.has_companions_var.get() else None

            # Book the room
            booking_id = db.book_guest_house(customer_id, room_id, check_out_date, id_card, None, meal_plan, companions)

            # Refresh the data
            self.refresh_guest_house()

            # Clear the form
            self.clear_booking_form()

            # Show success message
            guest_text = f" with {companion_count} companions" if companions else ""
            self.status_text.set(f"Room {room_number} booked successfully{guest_text}")
            messagebox.showinfo("Success", f"Room {room_number} booked successfully{guest_text}")

        except Exception as e:
            messagebox.showerror("Error", f"Error booking room: {str(e)}")

    def check_out_guest(self):
        """Check out a guest"""
        if not hasattr(self, 'cur_selected_booking_id') or not self.cur_selected_booking_id:
            messagebox.showwarning("Selection Required", "Please select a booking to check out")
            return

        # Confirm check-out
        if not messagebox.askyesno("Confirm Check-out", "Are you sure you want to check out this guest?"):
            return

        try:
            # Get room ID for the booking
            self.cur.execute("SELECT room_id FROM guest_house_bookings WHERE id = ?", (self.cur_selected_booking_id,))
            room_id = self.cur.fetchone()[0]

            # Update booking status
            self.cur.execute("UPDATE guest_house_bookings SET status = 'Completed' WHERE id = ?", (self.cur_selected_booking_id,))

            # Update room status
            self.cur.execute("UPDATE guest_house SET status = 'Available' WHERE id = ?", (room_id,))

            self.conn.commit()

            # Refresh the data
            self.refresh_guest_house()

            # Clear the form
            self.clear_booking_form()

            # Show success message
            self.status_text.set("Guest checked out successfully")
            messagebox.showinfo("Success", "Guest checked out successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Error checking out guest: {str(e)}")

    def clear_booking_form(self):
        """Clear the booking form fields"""
        if len(self.booking_customer_combo['values']) > 0:
            self.booking_customer_combo.current(0)
        if len(self.booking_room_combo['values']) > 0:
            self.booking_room_combo.current(0)
        self.booking_checkin_var.set(datetime.datetime.now().strftime("%Y-%m-%d"))
        self.booking_checkout_var.set("")
        self.booking_idcard_var.set("")
        self.booking_meal_var.set("None")

        # Clear companions
        self.has_companions_var.set(False)
        self.companions_list = []
        self.companions_listbox.delete(0, tk.END)
        self.companions_frame.grid_remove()

        if hasattr(self, 'cur_selected_booking_id'):
            delattr(self, 'cur_selected_booking_id')

    def validate_booking_form(self):
        """Validate booking form inputs"""
        if not self.booking_customer_var.get():
            messagebox.showwarning("Validation Error", "Please select a customer")
            return False

        if not self.booking_room_var.get():
            messagebox.showwarning("Validation Error", "Please select a room")
            return False

        if not self.booking_checkout_var.get():
            messagebox.showwarning("Validation Error", "Check-out date is required")
            return False

        # Validate check-out date format
        try:
            checkout_date = datetime.datetime.strptime(self.booking_checkout_var.get(), "%Y-%m-%d")
            checkin_date = datetime.datetime.strptime(self.booking_checkin_var.get(), "%Y-%m-%d")

            if checkout_date <= checkin_date:
                messagebox.showwarning("Validation Error", "Check-out date must be after check-in date")
                return False

        except ValueError:
            messagebox.showwarning("Validation Error", "Check-out date must be in YYYY-MM-DD format")
            return False

        return True

    def book_specific_room(self, room_id):
        """Book a specific room from the dashboard"""
        # Get room details
        self.cur.execute("SELECT room_number FROM guest_house WHERE id = ?", (room_id,))
        room_number = self.cur.fetchone()[0]

        # Find the room in the combobox
        room_index = -1
        for i, value in enumerate(self.booking_room_combo['values']):
            if value.startswith(f"{room_number} - "):
                room_index = i
                break

        if room_index >= 0:
            # Switch to booking tab
            self.notebook.select(3)  # Index 3 is the Guest House tab

            # Select the room in the combobox
            self.booking_room_combo.current(room_index)

            # Set focus to customer selection
            self.booking_customer_combo.focus_set()

    def view_booking_details(self, room_id, booking_id):
        """View details of a booking"""
        # Get booking details
        self.cur.execute("""
            SELECT b.*, c.name as customer_name, g.room_number
            FROM guest_house_bookings b
            JOIN customers c ON b.customer_id = c.id
            JOIN guest_house g ON b.room_id = g.id
            WHERE b.id = ?
        """, (booking_id,))

        booking = self.cur.fetchone()

        if booking:
            # Create a details window
            details_window = tk.Toplevel(self)
            details_window.title(f"Booking Details - Room {booking['room_number']}")
            details_window.geometry("600x500")
            details_window.transient(self)

            # Create a notebook for tabs
            notebook = ttk.Notebook(details_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Main details tab
            details_tab = ttk.Frame(notebook)
            notebook.add(details_tab, text="Booking Details")

            # Add booking details
            ttk.Label(details_tab, text="Booking Details", font=("Arial", 16, "bold")).pack(pady=10)

            details_frame = ttk.Frame(details_tab)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            # Customer info
            ttk.Label(details_frame, text="Customer:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['customer_name']).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

            # Room info
            ttk.Label(details_frame, text="Room:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['room_number']).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

            # Check-in/out dates
            ttk.Label(details_frame, text="Check-in:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=f"{booking['check_in_date']} {booking['check_in_time']}").grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

            ttk.Label(details_frame, text="Check-out:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['check_out_date']).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

            # ID Card
            ttk.Label(details_frame, text="ID Card:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['id_card'] if booking['id_card'] else "N/A").grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)

            # Meal Plan
            ttk.Label(details_frame, text="Meal Plan:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['meal_plan'] if booking['meal_plan'] else "None").grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)

            # Total Amount
            ttk.Label(details_frame, text="Total Amount:", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=f"₦{booking['total_amount']:.2f}").grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)

            # Status
            ttk.Label(details_frame, text="Status:", font=("Arial", 10, "bold")).grid(row=7, column=0, sticky=tk.W, padx=5, pady=5)
            ttk.Label(details_frame, text=booking['status']).grid(row=7, column=1, sticky=tk.W, padx=5, pady=5)

            # Companions tab (if has companions)
            if booking['has_companions']:
                companions_tab = ttk.Frame(notebook)
                notebook.add(companions_tab, text="Companions")

                # Get companions
                companions = db.get_booking_companions(booking_id)

                if companions:
                    ttk.Label(companions_tab, text="Guest Companions", font=("Arial", 16, "bold")).pack(pady=10)

                    # Create a treeview for companions
                    tree_frame = ttk.Frame(companions_tab)
                    tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                    companions_tree = ttk.Treeview(tree_frame, columns=("Name", "Relationship", "ID Card", "Phone"), show="headings")

                    # Define headings
                    companions_tree.heading("Name", text="Name")
                    companions_tree.heading("Relationship", text="Relationship")
                    companions_tree.heading("ID Card", text="ID Card")
                    companions_tree.heading("Phone", text="Phone")

                    # Define columns
                    companions_tree.column("Name", width=150)
                    companions_tree.column("Relationship", width=100)
                    companions_tree.column("ID Card", width=120)
                    companions_tree.column("Phone", width=120)

                    # Add scrollbar
                    scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=companions_tree.yview)
                    companions_tree.configure(yscroll=scrollbar.set)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                    companions_tree.pack(fill=tk.BOTH, expand=True)

                    # Add companions to tree
                    for companion in companions:
                        companions_tree.insert("", tk.END, values=(
                            companion['name'],
                            companion['relationship'] if companion['relationship'] else "N/A",
                            companion['id_card'] if companion['id_card'] else "N/A",
                            companion['phone'] if companion['phone'] else "N/A"
                        ))
                else:
                    ttk.Label(companions_tab, text="No companions found", font=("Arial", 12)).pack(pady=50)

            # Orders tab
            orders_tab = ttk.Frame(notebook)
            notebook.add(orders_tab, text="Orders")

            # Get orders for this booking
            self.cur.execute("""
                SELECT * FROM guest_house_orders WHERE booking_id = ? ORDER BY order_date DESC
            """, (booking_id,))

            orders = self.cur.fetchall()

            if orders:
                ttk.Label(orders_tab, text="Room Orders", font=("Arial", 16, "bold")).pack(pady=10)

                # Create a treeview for orders
                tree_frame = ttk.Frame(orders_tab)
                tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                orders_tree = ttk.Treeview(tree_frame, columns=("Date", "Type", "Items", "Amount"), show="headings")

                # Define headings
                orders_tree.heading("Date", text="Date")
                orders_tree.heading("Type", text="Type")
                orders_tree.heading("Items", text="Items")
                orders_tree.heading("Amount", text="Amount")

                # Define columns
                orders_tree.column("Date", width=150)
                orders_tree.column("Type", width=100)
                orders_tree.column("Items", width=200)
                orders_tree.column("Amount", width=100)

                # Add scrollbar
                scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=orders_tree.yview)
                orders_tree.configure(yscroll=scrollbar.set)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                orders_tree.pack(fill=tk.BOTH, expand=True)

                # Add orders to tree
                for order in orders:
                    orders_tree.insert("", tk.END, values=(
                        order['order_date'],
                        order['order_type'],
                        order['items'],
                        f"₦{order['total_amount']:.2f}"
                    ))
            else:
                ttk.Label(orders_tab, text="No orders found", font=("Arial", 12)).pack(pady=50)

            # Close button
            ttk.Button(details_window, text="Close", command=details_window.destroy).pack(pady=10)

    def add_room_order(self, booking_id):
        """Add an order for a room"""
        # Create an order window
        order_window = tk.Toplevel(self)
        order_window.title("Add Room Order")
        order_window.geometry("400x300")
        order_window.transient(self)

        # Add order form
        ttk.Label(order_window, text="Add Room Order", font=("Arial", 16, "bold")).pack(pady=10)

        form_frame = ttk.Frame(order_window)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Order type
        ttk.Label(form_frame, text="Order Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        order_type_var = tk.StringVar()
        order_type_combo = ttk.Combobox(form_frame, textvariable=order_type_var, width=20)
        order_type_combo['values'] = ('Room Service', 'Restaurant', 'Bar', 'Laundry', 'Other')
        order_type_combo.current(0)
        order_type_combo.grid(row=0, column=1, padx=5, pady=5)

        # Items
        ttk.Label(form_frame, text="Items:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        items_var = tk.StringVar()
        items_entry = ttk.Entry(form_frame, textvariable=items_var, width=30)
        items_entry.grid(row=1, column=1, padx=5, pady=5)

        # Amount
        ttk.Label(form_frame, text="Amount (₦):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        amount_var = tk.StringVar()
        amount_entry = ttk.Entry(form_frame, textvariable=amount_var, width=15)
        amount_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        # Add order button
        def add_order():
            try:
                order_type = order_type_var.get()
                items = items_var.get()
                amount = float(amount_var.get())

                if not items:
                    messagebox.showwarning("Validation Error", "Please enter items")
                    return

                if amount <= 0:
                    messagebox.showwarning("Validation Error", "Amount must be greater than zero")
                    return

                # Add order to database
                db.add_guest_house_order(booking_id, order_type, items, amount)

                # Refresh the data
                self.refresh_guest_house()

                # Close the window
                order_window.destroy()

                # Show success message
                self.status_text.set("Order added successfully")
                messagebox.showinfo("Success", "Order added successfully")

            except ValueError:
                messagebox.showwarning("Validation Error", "Amount must be a number")
            except Exception as e:
                messagebox.showerror("Error", f"Error adding order: {str(e)}")

        ttk.Button(form_frame, text="Add Order", command=add_order).grid(row=3, column=0, columnspan=2, pady=10)

        # Cancel button
        ttk.Button(form_frame, text="Cancel", command=order_window.destroy).grid(row=4, column=0, columnspan=2, pady=5)

    def refresh_room_dashboard(self):
        """Refresh the room dashboard"""
        # Clear existing room cards
        for widget in self.room_cards_frame.winfo_children():
            widget.destroy()

        # Get all rooms with their status
        rooms = db.get_guest_house_dashboard()

        # Create a grid layout for room cards
        row = 0
        col = 0

        for room in rooms:
            # Create a frame for each room
            room_frame = ttk.LabelFrame(self.room_cards_frame, text=f"Room {room[1]}: {room[2]}")
            room_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            # Room details
            ttk.Label(room_frame, text=f"Type: {room[2]}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Capacity: {room[3]} person(s)").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
            ttk.Label(room_frame, text=f"Rate: ₦{room[4]:.2f} per night").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

            # Status with color coding
            status_frame = ttk.Frame(room_frame)
            status_frame.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)

            status_color = "#4CAF50" if room[5] == "Available" else "#F44336"  # Green for available, red for occupied
            status_label = ttk.Label(status_frame, text=f"Status: {room[5]}", foreground=status_color, font=("Arial", 10, "bold"))
            status_label.pack(side=tk.LEFT)

            # If room is occupied, show occupant info
            if room[5] == "Occupied" and room[10]:  # room[10] is customer_id
                occupant_frame = ttk.LabelFrame(room_frame, text="Occupant Information")
                occupant_frame.grid(row=4, column=0, sticky="ew", padx=5, pady=5)

                ttk.Label(occupant_frame, text=f"Name: {room[11]}").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(occupant_frame, text=f"Check-in: {room[7]}").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
                ttk.Label(occupant_frame, text=f"Check-out: {room[9]}").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)

                # Add action buttons
                button_frame = ttk.Frame(room_frame)
                button_frame.grid(row=5, column=0, pady=5)

                ttk.Button(button_frame, text="View Details",
                          command=lambda r=room[0], b=room[6]: self.view_booking_details(r, b)).pack(side=tk.LEFT, padx=2)
                ttk.Button(button_frame, text="Add Order",
                          command=lambda b=room[6]: self.add_room_order(b)).pack(side=tk.LEFT, padx=2)
            else:
                # Add book button for available rooms
                ttk.Button(room_frame, text="Book Now",
                          command=lambda r=room[0]: self.book_specific_room(r)).grid(row=5, column=0, pady=5)

            # Update grid position
            col += 1
            if col > 2:  # 3 rooms per row
                col = 0
                row += 1

        # Make rows and columns expandable
        for i in range(3):
            self.room_cards_frame.columnconfigure(i, weight=1)
        for i in range(row + 1):
            self.room_cards_frame.rowconfigure(i, weight=1)

    def load_customer_data(self):
        """Load customer data for the booking form"""
        # Get all customers
        self.cur.execute("SELECT id, name FROM customers ORDER BY name")
        customers = self.cur.fetchall()

        # Format for combobox: "ID - Name"
        customer_list = [f"{c[0]} - {c[1]}" for c in customers]
        self.booking_customer_combo['values'] = customer_list

    def load_room_data(self):
        """Load room data for the booking form"""
        # Get all rooms
        self.cur.execute("SELECT id, room_number, room_type, status FROM guest_house ORDER BY room_number")
        rooms = self.cur.fetchall()

        # Format for combobox: "Room# - Type (Status)"
        room_list = [f"{r[1]} - {r[2]} ({r[3]})" for r in rooms]
        self.booking_room_combo['values'] = room_list

    # ===== CUSTOMERS TAB =====
    def setup_customers_tab(self):
        """Setup the customers management tab"""
        # Create frames
        form_frame = ttk.LabelFrame(self.customers_frame, text="Customer Details")
        form_frame.pack(fill=tk.X, padx=20, pady=10)

        list_frame = ttk.LabelFrame(self.customers_frame, text="Customer List")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Form fields
        # Row 0
        ttk.Label(form_frame, text="Name:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_name_var, width=30).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Rank:").grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        self.customer_rank_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_rank_var, width=15).grid(row=0, column=3, padx=5, pady=5)

        # Row 1
        ttk.Label(form_frame, text="Department:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_dept_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_dept_var, width=30).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(form_frame, text="Phone:").grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        self.customer_phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_phone_var, width=15).grid(row=1, column=3, padx=5, pady=5)

        # Row 2
        ttk.Label(form_frame, text="Email:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_email_var, width=30).grid(row=2, column=1, padx=5, pady=5)

        # Row 3
        ttk.Label(form_frame, text="Address:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.customer_address_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.customer_address_var, width=50).grid(row=3, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W+tk.E)

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=4, column=0, columnspan=4, pady=10)

        ttk.Button(button_frame, text="Add Customer", command=self.add_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Update", command=self.update_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete", command=self.delete_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=self.clear_customer_form).pack(side=tk.LEFT, padx=5)

        # Search frame
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        self.customer_search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.customer_search_var, width=30).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Search", command=self.search_customers).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="Show All", command=self.refresh_customers).pack(side=tk.LEFT, padx=5)

        # Customer list with scrollbar
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.customer_tree = ttk.Treeview(tree_frame, columns=("ID", "Name", "Rank", "Department", "Phone", "Email", "Address", "Registration"), show="headings")

        # Define headings
        self.customer_tree.heading("ID", text="ID")
        self.customer_tree.heading("Name", text="Name")
        self.customer_tree.heading("Rank", text="Rank")
        self.customer_tree.heading("Department", text="Department")
        self.customer_tree.heading("Phone", text="Phone")
        self.customer_tree.heading("Email", text="Email")
        self.customer_tree.heading("Address", text="Address")
        self.customer_tree.heading("Registration", text="Registration Date")

        # Define columns
        self.customer_tree.column("ID", width=50)
        self.customer_tree.column("Name", width=150)
        self.customer_tree.column("Rank", width=80)
        self.customer_tree.column("Department", width=120)
        self.customer_tree.column("Phone", width=100)
        self.customer_tree.column("Email", width=150)
        self.customer_tree.column("Address", width=200)
        self.customer_tree.column("Registration", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.customer_tree.yview)
        self.customer_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.customer_tree.pack(fill=tk.BOTH, expand=True)

        # Bind select event
        self.customer_tree.bind("<<TreeviewSelect>>", self.on_customer_select)

        # Initial data load
        self.refresh_customers()

    def refresh_customers(self):
        """Refresh the customer list"""
        # Clear the treeview
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # Get all customers
        self.cur.execute("SELECT * FROM customers")
        customers = self.cur.fetchall()

        # Add to treeview
        for customer in customers:
            # customer format: id, name, rank, department, phone, email, registration_date
            self.customer_tree.insert("", tk.END, values=customer)

    def on_customer_select(self, event):
        """Handle customer selection"""
        selected_item = self.customer_tree.selection()
        if not selected_item:
            return

        # Get the selected item values
        item = self.customer_tree.item(selected_item[0])
        values = item['values']

        # Fill the form fields
        self.customer_name_var.set(values[1])
        self.customer_rank_var.set(values[2] if values[2] else "")
        self.customer_dept_var.set(values[3] if values[3] else "")
        self.customer_phone_var.set(values[4] if values[4] else "")
        self.customer_email_var.set(values[5] if values[5] else "")
        self.customer_address_var.set(values[6] if values[6] else "")

        # Store the selected customer ID
        self.cur_selected_customer_id = values[0]

    def add_customer(self):
        """Add a new customer"""
        # Validate inputs
        if not self.validate_customer_form():
            return

        try:
            # Get form values
            name = self.customer_name_var.get()
            rank = self.customer_rank_var.get()
            department = self.customer_dept_var.get()
            phone = self.customer_phone_var.get()
            email = self.customer_email_var.get()
            address = self.customer_address_var.get()

            # Add to database
            customer_id = db.add_customer(name, rank, department, phone, email, address)

            # Refresh the list
            self.refresh_customers()

            # Clear the form
            self.clear_customer_form()

            # Show success message
            self.status_text.set(f"Customer '{name}' added")
            messagebox.showinfo("Success", f"Customer '{name}' added")

        except Exception as e:
            messagebox.showerror("Error", f"Error adding customer: {str(e)}")

    def update_customer(self):
        """Update an existing customer"""
        if not hasattr(self, 'cur_selected_customer_id') or not self.cur_selected_customer_id:
            messagebox.showwarning("Selection Required", "Please select a customer to update")
            return

        # Validate inputs
        if not self.validate_customer_form():
            return

        try:
            # Get form values
            name = self.customer_name_var.get()
            rank = self.customer_rank_var.get()
            department = self.customer_dept_var.get()
            phone = self.customer_phone_var.get()
            email = self.customer_email_var.get()
            address = self.customer_address_var.get()

            # Update in database
            self.cur.execute("""
                UPDATE customers
                SET name = ?, rank = ?, department = ?, phone = ?, email = ?, address = ?
                WHERE id = ?
            """, (name, rank, department, phone, email, address, self.cur_selected_customer_id))
            self.conn.commit()

            # Refresh the list
            self.refresh_customers()

            # Show success message
            self.status_text.set(f"Customer '{name}' updated")
            messagebox.showinfo("Success", f"Customer '{name}' updated")

        except Exception as e:
            messagebox.showerror("Error", f"Error updating customer: {str(e)}")

    def delete_customer(self):
        """Delete a customer"""
        if not hasattr(self, 'cur_selected_customer_id') or not self.cur_selected_customer_id:
            messagebox.showwarning("Selection Required", "Please select a customer to delete")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this customer?"):
            return

        try:
            # Check if customer has related records
            self.cur.execute("""
                SELECT COUNT(*) FROM (
                    SELECT customer_id FROM restaurant_orders WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM gym_memberships WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM guest_house_bookings WHERE customer_id = ?
                    UNION ALL
                    SELECT customer_id FROM invoices WHERE customer_id = ?
                )
            """, (self.cur_selected_customer_id, self.cur_selected_customer_id,
                  self.cur_selected_customer_id, self.cur_selected_customer_id))

            related_count = self.cur.fetchone()[0]

            if related_count > 0:
                if not messagebox.askyesno("Warning",
                                          "This customer has related records in other modules. "
                                          "Deleting will remove all related data. Continue?"):
                    return

            # Delete from database
            self.cur.execute("DELETE FROM customers WHERE id = ?", (self.cur_selected_customer_id,))
            self.conn.commit()

            # Refresh the list
            self.refresh_customers()

            # Clear the form
            self.clear_customer_form()

            # Show success message
            self.status_text.set("Customer deleted")

        except Exception as e:
            messagebox.showerror("Error", f"Error deleting customer: {str(e)}")

    def clear_customer_form(self):
        """Clear the customer form fields"""
        self.customer_name_var.set("")
        self.customer_rank_var.set("")
        self.customer_dept_var.set("")
        self.customer_phone_var.set("")
        self.customer_email_var.set("")
        self.customer_address_var.set("")
        if hasattr(self, 'cur_selected_customer_id'):
            delattr(self, 'cur_selected_customer_id')

    def validate_customer_form(self):
        """Validate customer form inputs"""
        if not self.customer_name_var.get():
            messagebox.showwarning("Validation Error", "Customer name is required")
            return False

        # Validate email format if provided
        email = self.customer_email_var.get()
        if email and '@' not in email:
            messagebox.showwarning("Validation Error", "Invalid email format")
            return False

        return True

    def search_customers(self):
        """Search customers"""
        search_term = self.customer_search_var.get().strip().lower()
        if not search_term:
            self.refresh_customers()
            return

        # Clear the treeview
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # Search in database
        customers = db.search_customers(search_term)

        # Add to treeview
        for customer in customers:
            self.customer_tree.insert("", tk.END, values=customer)

    # ===== INVOICES TAB =====
    def setup_invoices_tab(self):
        """Setup the invoices management tab"""
        # Placeholder for now
        ttk.Label(self.invoices_frame, text="Invoice Management - To be implemented").pack(pady=20)

    def refresh_invoices(self):
        """Refresh invoices data"""
        # Placeholder for now
        pass


# Main entry point
if __name__ == "__main__":
    try:
        # Try to import required packages
        import qrcode
        from PIL import Image, ImageTk
    except ImportError:
        import tkinter.messagebox as mb
        mb.showerror("Missing Dependencies",
                    "This application requires additional packages.\n"
                    "Please install them using pip:\n\n"
                    "pip install qrcode pillow")
        exit(1)

    app = HQLogisticsApp()
    app.mainloop()
